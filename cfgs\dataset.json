{"dataset": "diem", "index": "split1", "batch_size": 30, "n_threads": 6, "video_path_diem": "./data/video_frames/DIEM", "video_path_coutrot1": "./data/video_frames/Coutrot_db1", "video_path_coutrot2": "./data/video_frames/Coutrot_db2", "video_path_summe": "./data/video_frames/SumMe", "video_path_etmd": "./data/video_frames/ETMD_av", "video_path_avad": "./data/video_frames/AVAD", "audio_path_diem": "./data/video_audio/DIEM", "audio_path_coutrot1": "./data/video_audio/Coutrot_db1", "audio_path_coutrot2": "./data/video_audio/Coutrot_db2", "audio_path_summe": "./data/video_audio/SumMe", "audio_path_etmd": "./data/video_audio/ETMD_av", "audio_path_avad": "./data/video_audio/AVAD", "split1": {"annotation_path_diem_train": "./data/fold_lists/DIEM_list_train_fps.txt", "annotation_path_coutrot1_train": "./data/fold_lists/Coutrot_db1_list_train_1_fps.txt", "annotation_path_coutrot2_train": "./data/fold_lists/Coutrot_db2_list_train_1_fps.txt", "annotation_path_summe_train": "./data/fold_lists/SumMe_list_train_1_fps.txt", "annotation_path_etmd_train": "./data/fold_lists/ETMD_av_list_train_1_fps.txt", "annotation_path_avad_train": "./data/fold_lists/AVAD_list_train_1_fps.txt", "annotation_path_coutrot2_test": "./data/fold_lists/Coutrot_db2_list_test_1_fps.txt", "annotation_path_summe_test": "./data/fold_lists/SumMe_list_test_1_fps.txt", "annotation_path_etmd_test": "./data/fold_lists/ETMD_av_list_test_1_fps.txt", "annotation_path_avad_test": "./data/fold_lists/AVAD_list_test_1_fps.txt", "annotation_path_diem_test": "./data/fold_lists/DIEM_list_test_fps.txt", "annotation_path_coutrot1_test": "./data/fold_lists/Coutrot_db1_list_test_1_fps.txt"}, "split2": {"annotation_path_diem_train": "./data/fold_lists/DIEM_list_train_fps.txt", "annotation_path_coutrot1_train": "./data/fold_lists/Coutrot_db1_list_train_2_fps.txt", "annotation_path_coutrot2_train": "./data/fold_lists/Coutrot_db2_list_train_2_fps.txt", "annotation_path_summe_train": "./data/fold_lists/SumMe_list_train_2_fps.txt", "annotation_path_etmd_train": "./data/fold_lists/ETMD_av_list_train_2_fps.txt", "annotation_path_avad_train": "./data/fold_lists/AVAD_list_train_2_fps.txt", "annotation_path_coutrot2_test": "./data/fold_lists/Coutrot_db2_list_test_2_fps.txt", "annotation_path_summe_test": "./data/fold_lists/SumMe_list_test_2_fps.txt", "annotation_path_etmd_test": "./data/fold_lists/ETMD_av_list_test_2_fps.txt", "annotation_path_avad_test": "./data/fold_lists/AVAD_list_test_2_fps.txt", "annotation_path_diem_test": "./data/fold_lists/DIEM_list_test_fps.txt", "annotation_path_coutrot1_test": "./data/fold_lists/Coutrot_db1_list_test_2_fps.txt"}, "split3": {"annotation_path_diem_train": "./data/fold_lists/DIEM_list_train_fps.txt", "annotation_path_coutrot1_train": "./data/fold_lists/Coutrot_db1_list_train_3_fps.txt", "annotation_path_coutrot2_train": "./data/fold_lists/Coutrot_db2_list_train_3_fps.txt", "annotation_path_summe_train": "./data/fold_lists/SumMe_list_train_3_fps.txt", "annotation_path_etmd_train": "./data/fold_lists/ETMD_av_list_train_3_fps.txt", "annotation_path_avad_train": "./data/fold_lists/AVAD_list_train_3_fps.txt", "annotation_path_coutrot2_test": "./data/fold_lists/Coutrot_db2_list_test_3_fps.txt", "annotation_path_summe_test": "./data/fold_lists/SumMe_list_test_3_fps.txt", "annotation_path_etmd_test": "./data/fold_lists/ETMD_av_list_test_3_fps.txt", "annotation_path_avad_test": "./data/fold_lists/AVAD_list_test_3_fps.txt", "annotation_path_diem_test": "./data/fold_lists/DIEM_list_test_fps.txt", "annotation_path_coutrot1_test": "./data/fold_lists/Coutrot_db1_list_test_3_fps.txt"}, "salmap_path_diem": "./data/annotations/DIEM", "salmap_path_coutrot1": "./data/annotations/Coutrot_db1", "salmap_path_coutrot2": "./data/annotations/Coutrot_db2", "salmap_path_summe": "./data/annotations/SumMe", "salmap_path_etmd": "./data/annotations/ETMD_av", "salmap_path_avad": "./data/annotations/AVAD", "sample_size": [384, 224], "sample_duration": 16, "no_mean_norm": false, "norm_value": 1, "std_norm": false, "mean": [114.7748, 107.7354, 99.475], "std": [38.7568578, 37.88248729, 40.02898126], "no_hflip": false, "use_spectrum": true, "audio_dict": {"soundnet": "ori", "vggish": "mel", "avid_spec": "spec", "visual_only": "none"}, "audio_type": "mel", "with_audio": true}