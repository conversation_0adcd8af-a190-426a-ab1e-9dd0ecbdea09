"""
AV-DART扩散包装器模型
将AV-DART模型包装成扩散模型接口，使其能够与DiffusionTrainer兼容
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import sys
import os

# 添加路径以支持导入
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(os.path.dirname(os.path.dirname(current_dir)))
sys.path.insert(0, project_root)
sys.path.insert(0, current_dir)


from DART.av_dart.models.enhanced_av_dart_model import EnhancedAVDARTModel



class AVDARTDiffusionWrapper(nn.Module):
    """
    将AV-DART模型包装成扩散模型接口
    使其能够与现有的DiffusionTrainer兼容
    """
    
    def __init__(
        self,
        embed_dim: int = 768,
        total_tokens: int = 196,
        num_layers: int = 6,
        num_heads: int = 8,
        visual_size: int = 224,
        audio_size: tuple = (64, 224),
        output_size: tuple = (224, 384),
        token_strategy: str = "dynamic",
        **strategy_kwargs
    ):
        super().__init__()
        
        # 检查EnhancedAVDARTModel是否可用
        if EnhancedAVDARTModel is None:
            raise ImportError("无法导入EnhancedAVDARTModel，请检查路径设置")
        
        # 核心增强版AV-DART模型
        self.av_dart_model = EnhancedAVDARTModel(
            embed_dim=embed_dim,
            total_tokens=total_tokens,
            num_transformer_layers=num_layers,
            num_heads=num_heads,
            visual_size=visual_size,
            audio_size=audio_size,
            output_size=output_size,
            token_strategy=token_strategy,
            # 启用所有升级功能
            use_pyramid_dart=True,
            use_rope=True,
            use_gated_fusion=True,
            use_center_bias=True,
            use_multiscale_supervision=True,
            use_multi_metric_loss=True,
            **strategy_kwargs
        )
        
        # 模拟原始模型的属性，保持兼容性
        self.audio_net = True  # 表示有音频网络
        # 注意: visual_net 将作为方法定义，不再在这里设置为属性
        self.spatiotemp_net = None  # AV-DART内部处理时空关系，不需要额外的spatiotemp_net
        
        # 创建虚拟的visual_net来满足DiffusionTrainer的期望
        self.visual_backbone = self._create_visual_backbone()
        
        # 时间嵌入层（用于扩散过程）
        self.time_embedding_dim = 128
        self.time_mlp = nn.Sequential(
            nn.Linear(self.time_embedding_dim, embed_dim),
            nn.ReLU(),
            nn.Linear(embed_dim, embed_dim)
        )
        
        # 时间投影层（将时间嵌入投影到输出通道数）
        output_channels = 1  # 显著性图通常是单通道
        self.time_proj = nn.Linear(embed_dim, output_channels)
        
    def _create_visual_backbone(self):
        """创建虚拟的视觉骨干网络，返回多尺度特征"""
        class VirtualVisualNet(nn.Module):
            def __init__(self):
                super().__init__()
                # 简单的虚拟网络，返回原始diffusion模型期望的多尺度特征格式
                
            def forward(self, x):
                # 返回4个尺度的特征，匹配原始VideoSaliencyModel的输出格式
                B = x.size(0)
                device = x.device
                
                # 模拟MViT的多尺度输出: [768, 384, 192, 96]通道数，不同空间分辨率
                vis_list = [
                    torch.randn((B, 768, 8, 7, 12), device=device),   # scale 0
                    torch.randn((B, 384, 8, 14, 24), device=device),  # scale 1  
                    torch.randn((B, 192, 8, 28, 48), device=device),  # scale 2
                    torch.randn((B, 96, 8, 56, 96), device=device),   # scale 3
                ]
                return vis_list
                
        return VirtualVisualNet()
        
    def get_timestep_embedding(self, timesteps, embedding_dim):
        """
        生成时间步嵌入（来自原始扩散模型）
        """
        assert len(timesteps.shape) == 1
        half_dim = embedding_dim // 2
        emb = torch.log(torch.tensor(10000.0)) / (half_dim - 1)
        emb = torch.exp(torch.arange(half_dim, dtype=torch.float32, device=timesteps.device) * -emb)
        emb = timesteps.float()[:, None] * emb[None, :]
        emb = torch.cat([torch.sin(emb), torch.cos(emb)], dim=1)
        if embedding_dim % 2 == 1:  # zero pad
            emb = F.pad(emb, (0, 1))
        return emb
    
    def forward(self, data, t_tensor):
        """
        扩散模型训练的前向传播接口
        
        Args:
            data: 字典，包含
                - "img": 图像特征 [B, C, H, W]
                - "input": 噪声输入 [B, C, H, W]  
                - "train_obj": 训练目标 [B, C, H, W]
                - "audio": 音频特征 [B, ...]
            t_tensor: 时间步 [B]
            
        Returns:
            pred: 预测结果 [B, C, H, W]
        """
        # 提取输入数据
        imgs = data["img"]  # 视觉输入
        audio = data["audio"]  # 音频输入
        x_noisy = data["input"]  # 噪声输入（扩散过程的当前状态）
        
        # 生成时间嵌入
        time_emb = self.get_timestep_embedding(t_tensor, self.time_embedding_dim)
        time_emb = self.time_mlp(time_emb)  # [B, embed_dim]
        
        # 使用AV-DART模型预测显著性图
        saliency_pred = self.av_dart_model(imgs, audio)
        
        # 将时间信息融入预测结果
        # 这里可以通过多种方式融合时间信息，比如：
        # 1. 直接相加（最简单）
        # 2. 通过额外的网络层融合
        # 3. 作为条件信息
        
        # 方法1：通过空间广播融合时间嵌入
        B, C, H, W = saliency_pred.shape
        time_emb_spatial = time_emb.view(B, -1, 1, 1)  # [B, embed_dim, 1, 1]
        
        # 将时间嵌入调整到与显著性图相同的通道数
        time_emb_proj = self.time_proj(time_emb).unsqueeze(-1).unsqueeze(-1)  # [B, C, 1, 1]
        
        # 融合时间信息和显著性预测
        saliency_pred_with_time = saliency_pred + 0.1 * time_emb_proj.expand_as(saliency_pred)
        
        return saliency_pred_with_time
    
    def forward_vggish(self, audio_input):
        """
        增强的forward_vggish方法，与原始模型兼容
        返回符合DiffusionTrainer期望的音频特征格式
        """
        if audio_input is None:
            B = 4  # 默认batch size
            device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        else:
            B = audio_input.size(0)
            device = audio_input.device
            
        # 模拟VGGish的输出格式: [B, C, T, H, W]
        # 根据原始模型，VGGish输出的特征图尺寸为 [B, 128, T, H, W]
        bs, T = B, 8  # 默认时间序列长度
        
        # 模拟VGGish的特征输出：[B, 128, T, H, W]
        audio_feat_map = torch.randn(bs, 128, T, 6, 4, device=device)
        
        # 返回格式与原始diff_model.py中forward_vggish一致
        # audio_feat_map 作为两个返回值（特征图和嵌入特征）
        return audio_feat_map, audio_feat_map
    
    def visual_net(self, visual_input):
        """
        视觉网络接口，返回多尺度特征
        与原始MViT的输出格式保持一致
        """
        return self.visual_backbone(visual_input)
    
    def decoder_net(self, x, t, vis_feat, audio_feat_embed=None, **kwargs):
        """
        增强的decoder_net方法，支持完整的扩散模型接口
        与原始模型的SalUNet解码器兼容
        
        Args:
            x: 噪声输入 [B, C, H, W]
            t: 时间步 [B] 或 整数
            vis_feat: 视觉特征列表 [feat0, feat1, feat2, feat3]
            audio_feat_embed: 音频嵌入特征 [B, 512] 或 [B, 128, T, H, W]
            **kwargs: 其他参数
            
        Returns:
            pred: 预测结果 [B, C, H, W]
        """
        # 处理时间步参数
        if isinstance(t, int):
            B = x.size(0)
            t_tensor = torch.full((B,), t, dtype=torch.int64, device=x.device)
        else:
            t_tensor = t
            
        # 从视觉特征中提取主要特征（通常是第一个或最后一个）
        if isinstance(vis_feat, list) and len(vis_feat) > 0:
            # 使用最高分辨率的特征作为主要视觉输入
            main_vis_feat = vis_feat[-1]  # 通常是最高分辨率的特征
            
            # 将 [B, C, T, H, W] 转换为 [B*T, C, H, W] 或 [B, C, H, W]
            if main_vis_feat.dim() == 5:
                B, C, T, H, W = main_vis_feat.shape
                # 取平均或中间帧
                main_vis_feat = main_vis_feat.mean(dim=2)  # [B, C, H, W]
                
            # 调整尺寸以匹配音频输入尺寸
            if main_vis_feat.shape[-2:] != (224, 384):
                main_vis_feat = F.interpolate(
                    main_vis_feat, size=(224, 384), 
                    mode='bilinear', align_corners=False
                )
        else:
            # 如果没有视觉特征，创建虚拟特征
            B = x.size(0)
            main_vis_feat = torch.randn(B, 3, 224, 384, device=x.device)
            
        # 处理音频特征
        if audio_feat_embed is not None:
            if audio_feat_embed.dim() == 5:  # [B, C, T, H, W]
                B, C, T, H, W = audio_feat_embed.shape
                # 简化为二维特征 [B, feature_dim]
                audio_feat = audio_feat_embed.view(B, -1)[:, :1024]  # 截取前1024维
            elif audio_feat_embed.dim() == 2:  # [B, feature_dim]
                audio_feat = audio_feat_embed
            else:
                # 其他格式，展平处理
                audio_feat = audio_feat_embed.view(audio_feat_embed.size(0), -1)[:, :1024]
        else:
            # 创建虚拟音频特征
            B = x.size(0)
            audio_feat = torch.randn(B, 512, device=x.device)
            
        # 构造AV-DART的输入数据字典
        data = {
            "img": main_vis_feat,
            "input": x,
            "train_obj": x,  # 在推理时这个不重要
            "audio": audio_feat
        }
        
        # 调用AV-DART模型的包装器forward方法
        return self.forward(data, t_tensor)
    
    def get_model_info(self):
        """获取模型信息"""
        return {
            "type": "AVDARTDiffusionWrapper",
            "core_model": "AV-DART",
            "av_dart_info": self.av_dart_model.get_model_complexity(),
            "wrapper_params": sum(p.numel() for p in self.time_mlp.parameters())
        }


def create_av_dart_diffusion_wrapper(**kwargs):
    """创建AV-DART扩散包装器的便捷函数"""
    return AVDARTDiffusionWrapper(**kwargs)