"""
跨模态预算控制器

AV-DART的预算分配核心，负责：
- 保证总token数量固定：N_total = N_v + N_a
- 根据内容重要性动态分配视觉/音频token比例
- 实现多种预算分配策略
- 支持自适应预算调整
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Dict, Tuple, Optional, Union
import math


class BudgetController(nn.Module):
    """
    跨模态预算控制器
    
    核心思想：在固定总token预算下，根据内容特性动态分配视觉和音频tokens
    
    支持策略：
    - "fixed": 固定比例分配
    - "dynamic": 基于内容重要性动态分配  
    - "adaptive": 基于注意力机制自适应分配
    - "energy_based": 基于音频能量分配
    
    Args:
        total_tokens: 总token预算
        strategy: 分配策略
        visual_default: 视觉默认token数
        audio_default: 音频默认token数
        min_visual_ratio: 最小视觉token比例
        max_visual_ratio: 最大视觉token比例
    """
    
    def __init__(
        self,
        total_tokens: int = 196,
        strategy: str = "dynamic",
        visual_default: int = 144,  # 12x12
        audio_default: int = 52,    # 4x13 
        min_visual_ratio: float = 0.3,
        max_visual_ratio: float = 0.8,
        **kwargs
    ):
        super().__init__()
        
        self.total_tokens = total_tokens
        self.strategy = strategy
        self.visual_default = visual_default
        self.audio_default = audio_default
        self.min_visual_ratio = min_visual_ratio
        self.max_visual_ratio = max_visual_ratio
        
        # 计算默认比例
        default_total = visual_default + audio_default
        self.default_visual_ratio = visual_default / default_total
        self.default_audio_ratio = audio_default / default_total
        
        # 策略特定的模块
        if strategy == "adaptive":
            self.adaptive_controller = AdaptiveBudgetController(
                total_tokens, **kwargs
            )
        elif strategy == "energy_based":
            self.energy_controller = EnergyBasedController(
                total_tokens, **kwargs
            )
        elif strategy == "attention_based":
            self.attention_controller = AttentionBasedController(
                total_tokens, **kwargs
            )
    
    def forward(
        self,
        visual_input: torch.Tensor,
        audio_input: Union[torch.Tensor, Dict],
        return_details: bool = False
    ) -> Union[Dict, Tuple[int, int]]:
        """
        预算分配
        
        Args:
            visual_input: 视觉输入 [B, C, H, W] 或 [B, T, C, H, W]
            audio_input: 音频输入
            return_details: 是否返回详细信息
            
        Returns:
            budget_info: 包含 'visual_tokens', 'audio_tokens' 的字典
            或 (N_v, N_a) 元组
        """
        B = visual_input.size(0)
        
        if self.strategy == "fixed":
            allocation = self._fixed_allocation()
            
        elif self.strategy == "dynamic":
            allocation = self._dynamic_allocation(visual_input, audio_input)
            
        elif self.strategy == "adaptive":
            allocation = self.adaptive_controller(visual_input, audio_input)
            
        elif self.strategy == "energy_based":
            allocation = self.energy_controller(visual_input, audio_input)
            
        elif self.strategy == "attention_based":
            allocation = self.attention_controller(visual_input, audio_input)
            
        else:
            raise ValueError(f"Unknown strategy: {self.strategy}")
        
        # 确保预算约束
        allocation = self._enforce_budget_constraints(allocation)
        
        if return_details:
            return {
                'visual_tokens': allocation['N_v'],
                'audio_tokens': allocation['N_a'],
                'total_tokens': allocation['N_v'] + allocation['N_a'],
                'visual_ratio': allocation['N_v'] / self.total_tokens,
                'audio_ratio': allocation['N_a'] / self.total_tokens,
                'strategy': self.strategy,
                'allocation_details': allocation.get('details', {})
            }
        else:
            return allocation
    
    def _fixed_allocation(self) -> Dict:
        """固定比例分配"""
        N_v = int(self.total_tokens * self.default_visual_ratio)
        N_a = self.total_tokens - N_v
        
        return {
            'N_v': N_v,
            'N_a': N_a,
            'details': {
                'visual_ratio': self.default_visual_ratio,
                'audio_ratio': self.default_audio_ratio
            }
        }
    
    def _dynamic_allocation(
        self, 
        visual_input: torch.Tensor, 
        audio_input: Union[torch.Tensor, Dict]
    ) -> Dict:
        """基于内容特性的动态分配"""
        
        # 分析视觉复杂度
        visual_complexity = self._analyze_visual_complexity(visual_input)
        
        # 分析音频复杂度
        audio_complexity = self._analyze_audio_complexity(audio_input)
        
        # 计算相对重要性
        total_complexity = visual_complexity + audio_complexity + 1e-8
        visual_importance = visual_complexity / total_complexity
        audio_importance = audio_complexity / total_complexity
        
        # 分配tokens（带平滑）
        base_visual_ratio = self.default_visual_ratio
        adjustment = 0.3 * (visual_importance - 0.5)  # 最大调整30%
        
        visual_ratio = base_visual_ratio + adjustment
        visual_ratio = torch.clamp(
            visual_ratio, 
            self.min_visual_ratio, 
            self.max_visual_ratio
        ).item()
        
        N_v = int(self.total_tokens * visual_ratio)  
        N_a = self.total_tokens - N_v
        
        return {
            'N_v': N_v,
            'N_a': N_a,
            'details': {
                'visual_complexity': visual_complexity.item(),
                'audio_complexity': audio_complexity.item(),
                'visual_importance': visual_importance.item(),
                'audio_importance': audio_importance.item(),
                'visual_ratio': visual_ratio,
                'adjustment': adjustment
            }
        }
    
    def _analyze_visual_complexity(self, visual_input: torch.Tensor) -> torch.Tensor:
        """
        分析视觉复杂度
        
        基于梯度强度、纹理复杂度等指标
        """
        if visual_input.dim() == 5:  # [B, T, C, H, W]
            visual_input = visual_input.mean(dim=1)  # 平均时间维度
        
        B, C, H, W = visual_input.shape
        
        # 计算梯度强度（Sobel算子）
        gray = visual_input.mean(dim=1, keepdim=True)  # [B, 1, H, W]
        
        # 梯度计算
        grad_x = F.conv2d(gray, self._get_sobel_x().to(visual_input.device), padding=1)
        grad_y = F.conv2d(gray, self._get_sobel_y().to(visual_input.device), padding=1)
        gradient_magnitude = torch.sqrt(grad_x ** 2 + grad_y ** 2 + 1e-8)
        
        # 平均梯度强度作为复杂度指标
        complexity = gradient_magnitude.mean(dim=[1, 2, 3])  # [B]
        
        return complexity.mean()  # 批次平均
    
    def _analyze_audio_complexity(self, audio_input: Union[torch.Tensor, Dict]) -> torch.Tensor:
        """
        分析音频复杂度
        
        基于谱图的方差、能量分布等指标
        """
        # 获取谱图
        if isinstance(audio_input, dict):
            spectrogram = audio_input.get('spectrogram', None)
            if spectrogram is None:
                # 假设有原始波形
                waveform = audio_input.get('waveform', None)
                if waveform is not None:
                    # 使用波形方差作为复杂度
                    return waveform.var(dim=-1).mean()
                else:
                    return torch.tensor(0.5)  # 默认中等复杂度
        else:
            if audio_input.dim() == 2:  # 波形
                return audio_input.var(dim=-1).mean()
            elif audio_input.dim() == 4:  # 谱图 [B, 1, T, F]
                spectrogram = audio_input
            else:
                return torch.tensor(0.5)
        
        B, C, T, F = spectrogram.shape
        
        # 计算谱图复杂度：频率维度方差 + 时间维度方差
        freq_var = spectrogram.var(dim=-1).mean()  # 频率维度方差
        time_var = spectrogram.var(dim=-2).mean()  # 时间维度方差
        
        complexity = freq_var + time_var
        
        return complexity
    
    def _get_sobel_x(self) -> torch.Tensor:
        """Sobel X 算子"""
        return torch.tensor([
            [[-1, 0, 1],
             [-2, 0, 2], 
             [-1, 0, 1]]
        ], dtype=torch.float32).unsqueeze(0)
    
    def _get_sobel_y(self) -> torch.Tensor:
        """Sobel Y 算子"""
        return torch.tensor([
            [[-1, -2, -1],
             [0,   0,  0],
             [1,   2,  1]]
        ], dtype=torch.float32).unsqueeze(0)
    
    def _enforce_budget_constraints(self, allocation: Dict) -> Dict:
        """
        强制预算约束
        
        确保 N_v + N_a = total_tokens 且满足最小/最大比例限制
        """
        N_v = allocation['N_v']
        N_a = allocation['N_a']
        
        # 确保总数匹配
        if N_v + N_a != self.total_tokens:
            N_a = self.total_tokens - N_v
            
        # 强制比例限制
        min_N_v = int(self.total_tokens * self.min_visual_ratio)
        max_N_v = int(self.total_tokens * self.max_visual_ratio)
        
        N_v = max(min_N_v, min(N_v, max_N_v))
        N_a = self.total_tokens - N_v
        
        # 更新分配结果
        allocation['N_v'] = N_v
        allocation['N_a'] = N_a
        
        return allocation


class AdaptiveBudgetController(nn.Module):
    """
    自适应预算控制器
    
    使用可学习的网络根据输入特征自适应分配预算
    """
    
    def __init__(
        self,
        total_tokens: int,
        embed_dim: int = 256,
        hidden_dim: int = 128
    ):
        super().__init__()
        
        self.total_tokens = total_tokens
        
        # 特征提取网络
        self.visual_encoder = nn.Sequential(
            nn.AdaptiveAvgPool2d((7, 7)),
            nn.Flatten(),
            nn.Linear(7 * 7 * 3, hidden_dim),  # 假设RGB输入
            nn.ReLU(),
            nn.Linear(hidden_dim, embed_dim // 2)
        )
        
        self.audio_encoder = nn.Sequential(
            nn.AdaptiveAvgPool2d((4, 4)),
            nn.Flatten(), 
            nn.Linear(4 * 4, hidden_dim),  # 假设单通道音频
            nn.ReLU(),
            nn.Linear(hidden_dim, embed_dim // 2)
        )
        
        # 决策网络
        self.decision_net = nn.Sequential(
            nn.Linear(embed_dim, hidden_dim),
            nn.ReLU(),
            nn.Dropout(0.1),
            nn.Linear(hidden_dim, 1),
            nn.Sigmoid()  # 输出视觉token比例
        )
    
    def forward(
        self,
        visual_input: torch.Tensor,
        audio_input: Union[torch.Tensor, Dict]
    ) -> Dict:
        """
        自适应预算分配
        
        Args:
            visual_input: [B, C, H, W] 或 [B, T, C, H, W]
            audio_input: 音频输入
            
        Returns:
            allocation: 预算分配字典
        """
        # 处理视觉输入
        if visual_input.dim() == 5:
            B, T, C, H, W = visual_input.shape
            visual_input = visual_input.view(B * T, C, H, W)
        
        B = visual_input.size(0)
        
        # 提取视觉特征
        visual_feat = self.visual_encoder(visual_input)  # [B, embed_dim//2]
        
        # 处理音频输入
        if isinstance(audio_input, dict):
            audio_tensor = audio_input.get('spectrogram', None)
            if audio_tensor is None:
                audio_tensor = audio_input.get('waveform', None)
                if audio_tensor is not None and audio_tensor.dim() == 2:
                    # 将波形转为伪谱图格式用于编码
                    audio_tensor = audio_tensor.unsqueeze(1).unsqueeze(1)  # [B, 1, 1, L]
        else:
            audio_tensor = audio_input
            
        if audio_tensor.dim() == 4:  # [B, 1, T, F]
            audio_feat = self.audio_encoder(audio_tensor.squeeze(1))  # [B, embed_dim//2]
        else:
            # 处理
            audio_feat = torch.randn(B, visual_feat.size(1), device=visual_feat.device)
        
        # 拼接特征
        joint_feat = torch.cat([visual_feat, audio_feat], dim=1)  # [B, embed_dim]
        
        # 预测视觉token比例
        visual_ratio = self.decision_net(joint_feat).mean()  # 批次平均
        
        # 分配tokens
        N_v = int(self.total_tokens * visual_ratio.item())
        N_a = self.total_tokens - N_v
        
        return {
            'N_v': N_v,
            'N_a': N_a,
            'details': {
                'visual_ratio': visual_ratio.item(),
                'predicted_ratio': visual_ratio.item()
            }
        }


class EnergyBasedController(nn.Module):
    """
    基于音频能量的预算控制器
    
    根据音频能量强度调整视觉-音频token分配比例
    高音频能量 -> 更多音频tokens
    低音频能量 -> 更多视觉tokens
    """
    
    def __init__(
        self,
        total_tokens: int,
        energy_weight: float = 0.4,
        base_visual_ratio: float = 0.7
    ):
        super().__init__()
        
        self.total_tokens = total_tokens
        self.energy_weight = energy_weight
        self.base_visual_ratio = base_visual_ratio
    
    def forward(
        self,
        visual_input: torch.Tensor,
        audio_input: Union[torch.Tensor, Dict]
    ) -> Dict:
        """
        基于能量的预算分配
        """
        # 提取音频能量
        audio_energy = self._extract_audio_energy(audio_input)
        
        # 基于能量调整视觉token比例
        # 高能量音频 -> 减少视觉tokens, 增加音频tokens
        energy_adjustment = self.energy_weight * (audio_energy - 0.5)  # [-0.2, +0.2]
        visual_ratio = self.base_visual_ratio - energy_adjustment
        visual_ratio = torch.clamp(visual_ratio, 0.3, 0.8).item()
        
        N_v = int(self.total_tokens * visual_ratio)
        N_a = self.total_tokens - N_v
        
        return {
            'N_v': N_v,
            'N_a': N_a,
            'details': {
                'audio_energy': audio_energy.item(),
                'energy_adjustment': energy_adjustment.item(),
                'visual_ratio': visual_ratio
            }
        }
    
    def _extract_audio_energy(self, audio_input: Union[torch.Tensor, Dict]) -> torch.Tensor:
        """提取音频能量"""
        if isinstance(audio_input, dict):
            if 'energy' in audio_input:
                return audio_input['energy'].mean()
            elif 'spectrogram' in audio_input:
                spec = audio_input['spectrogram']
                return spec.pow(2).mean()
            elif 'waveform' in audio_input:
                waveform = audio_input['waveform'] 
                return waveform.pow(2).mean()
        else:
            if audio_input.dim() == 2:  # 波形
                return audio_input.pow(2).mean()
            elif audio_input.dim() == 4:  # 谱图
                return audio_input.pow(2).mean()
        
        return torch.tensor(0.5)  # 默认中等能量


class AttentionBasedController(nn.Module):
    """
    基于注意力的预算控制器
    
    使用跨模态注意力机制决定token分配
    """
    
    def __init__(
        self, 
        total_tokens: int,
        embed_dim: int = 256,
        num_heads: int = 4
    ):
        super().__init__()
        
        self.total_tokens = total_tokens
        self.embed_dim = embed_dim
        
        # 特征编码器
        self.visual_proj = nn.Linear(3, embed_dim)  # RGB -> embed_dim
        self.audio_proj = nn.Linear(1, embed_dim)   # 单通道 -> embed_dim
        
        # 跨模态注意力
        self.cross_attention = nn.MultiheadAttention(
            embed_dim, num_heads, batch_first=True
        )
        
        # 预算决策网络
        self.budget_head = nn.Sequential(
            nn.Linear(embed_dim, embed_dim // 2),
            nn.ReLU(),
            nn.Linear(embed_dim // 2, 1),
            nn.Sigmoid()
        )
    
    def forward(
        self,
        visual_input: torch.Tensor,
        audio_input: Union[torch.Tensor, Dict]
    ) -> Dict:
        """
        基于注意力的预算分配
        """
        # 特征提取（实际应用中需要更复杂的编码器）
        B = visual_input.size(0)
        
        # 视觉特征：全局平均池化
        if visual_input.dim() == 5:
            visual_input = visual_input.mean(dim=1)  # 平均时间维度
        
        visual_feat = F.adaptive_avg_pool2d(visual_input, (1, 1))  # [B, C, 1, 1]
        visual_feat = visual_feat.view(B, -1)  # [B, C]
        visual_feat = self.visual_proj(visual_feat[:, :3])  # [B, embed_dim]
        
        # 音频特征：类似处理
        if isinstance(audio_input, dict):
            audio_tensor = audio_input.get('spectrogram', torch.randn(B, 1, 64, 224))
        else:
            audio_tensor = audio_input
            
        if audio_tensor.dim() == 4:
            audio_feat = F.adaptive_avg_pool2d(audio_tensor, (1, 1))  # [B, C, 1, 1]
            audio_feat = audio_feat.view(B, -1)  # [B, C]
            audio_feat = self.audio_proj(audio_feat[:, :1])  # [B, embed_dim]
        else:
            audio_feat = torch.randn(B, self.embed_dim, device=visual_feat.device)
        
        # 跨模态注意力：视觉查询音频
        visual_feat = visual_feat.unsqueeze(1)  # [B, 1, embed_dim]
        audio_feat = audio_feat.unsqueeze(1)    # [B, 1, embed_dim]
        
        attended_feat, attn_weights = self.cross_attention(
            visual_feat, audio_feat, audio_feat
        )
        
        # 预算决策
        visual_ratio = self.budget_head(attended_feat.squeeze(1)).mean()  # 批次平均
        
        N_v = int(self.total_tokens * visual_ratio.item())
        N_a = self.total_tokens - N_v
        
        return {
            'N_v': N_v,
            'N_a': N_a,
            'details': {
                'visual_ratio': visual_ratio.item(),
                'attention_weights': attn_weights.detach()
            }
        }


# 便利函数
def create_budget_controller(
    total_tokens: int = 196,
    strategy: str = "dynamic",
    **kwargs
) -> BudgetController:
    """
    创建预算控制器的便利函数
    
    Args:
        total_tokens: 总token预算
        strategy: 分配策略
        **kwargs: 其他参数
        
    Returns:
        预算控制器实例
    """
    return BudgetController(
        total_tokens=total_tokens,
        strategy=strategy,
        **kwargs
    )


def create_simple_budget_controller(
    total_tokens: int = 196,
    visual_ratio: float = 0.73  # 默认约73%给视觉，27%给音频
) -> BudgetController:
    """
    创建简单固定比例预算控制器（占位版本）
    
    Args:
        total_tokens: 总token数
        visual_ratio: 视觉token比例
        
    Returns:
        固定比例预算控制器
    """
    visual_default = int(total_tokens * visual_ratio)
    audio_default = total_tokens - visual_default
    
    return BudgetController(
        total_tokens=total_tokens,
        strategy="fixed",
        visual_default=visual_default,
        audio_default=audio_default
    )