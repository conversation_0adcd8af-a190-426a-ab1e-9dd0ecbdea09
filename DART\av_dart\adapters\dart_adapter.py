"""
DART API适配器

封装DART核心功能，提供统一的动态tokenizer接口
支持视觉（图像/视频）和音频（log-mel谱图）的自适应分块
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Optional, Tuple, Union, Dict
import sys
import os
from timm.models.layers import DropPath, to_2tuple
from DART.av_dart.utils.spn import ScorePredNet
from DART.av_dart.utils.tools import *
# 添加DART路径
sys.path.append(os.path.join(os.path.dirname(__file__), '../..'))


class DART(nn.Module):
    """
    A dynamic tokenizer that extracts patches from an image based on content importance.
    Instead of a fixed grid, it samples variable-sized patches from more important regions,
    determined by a Score Prediction Network (SPN).
    """
    def __init__(
        self,
        img_size=224,
        patch_size=16,
        in_chans=3,
        embed_dim=768,
        norm_layer=None,
        spn=None,
        high_res=True,
        **kwargs
    ):
        super().__init__()
        stride = patch_size
        # Adjust image size for high-resolution input, processing it at half resolution.
        img_size = to_2tuple(img_size//2 if high_res else img_size)
        patch_size = to_2tuple(patch_size)

        self.img_size = img_size
        self.patch_size = patch_size
        # Calculate the number of patches in a standard grid layout.
        self.grid_size = (
            (img_size[0] - patch_size[0]) // stride + 1,
            (img_size[1] - patch_size[1]) // stride + 1
        )
        self.num_patches = self.grid_size[0] * self.grid_size[1]
        self.patch_size=16 # Explicitly set patch size for convolution

        # A simple convolutional layer to project fixed-size patches into embedding space.
        self.proj = nn.Conv2d(in_chans, embed_dim, kernel_size=self.patch_size, stride=self.patch_size)

        # Optional normalization layer for the embeddings.
        self.norm = norm_layer(embed_dim) if norm_layer else nn.Identity()

        self.dim = embed_dim
        # Score Prediction Network (SPN) to predict region importance. Uses a default if not provided.
        self.spn = spn or ScorePredNet(nn.Identity(), 3, 64)
        self.high_res = high_res

    def forward(self, x, ret_dict=False, pos_embed=None, target_h=14,num_patches=196):
        """
        Forward pass for the tokenizer.

        Args:
            x (torch.Tensor or dict): Input image tensor (B, C, H, W) or a dictionary containing it.
            ret_dict (bool): If True, returns a dictionary of intermediate values.
            pos_embed (torch.Tensor, optional): Positional embeddings to be resampled.
            target_h (int): The target number of vertical regions (rows).
            num_patches (int): The target total number of patches to extract.

        Returns:
            torch.Tensor or dict: The processed image patch embeddings, or a dictionary if ret_dict is True.
        """
        # Unpack input if it's a dictionary
        if isinstance(x, dict):
            target_h = x.get('target_h', None)
            num_patches = x.get('num_patches', None)
            x = x['x']
        B, C, H, W = x.shape
        
        # Determine the downsampling ratio based on high_res flag.
        down_ratio = 2 if self.high_res else 1
        # Assert that the input image dimensions match the model's configuration.
        assert H == self.img_size[0]*down_ratio and W == self.img_size[1]*down_ratio, (
            f"Input image size ({H}x{W}) doesn't match model "
            f"({self.img_size[0]}*{down_ratio}x{self.img_size[1]}*{down_ratio})."
        )
        ret = {'x': x} # Dictionary to store intermediate results
        org_x = x
        n = B
        
        # Interpolate the original image to a fixed size (224x224) for the SPN.
        org_x = F.interpolate(org_x, size=(224, 224), mode='bilinear', align_corners=False)
        # Predict importance scores for image regions using the SPN.
        try:
            score = self.spn(org_x)
            if score is None:
                raise ValueError("SPN returned None")
        except Exception as e:
            print(f"Warning: SPN failed ({e}), using uniform distribution")
            # 如果SPN失败，使用均匀分布
            score = torch.ones(B, 196, device=org_x.device, dtype=org_x.dtype)
            
        # Normalize scores to get a probability distribution function (PDF).
        pdf = score / (score.sum(dim=-1, keepdim=True) + 1e-8)  # (B, seqlen)

        ret['pdf'] = pdf
        ret['score'] = score
        
        # if pos_embed is not None:
            # Repeat positional embeddings for each item in the batch.
        
        # 初始化 pos_embed
        if pos_embed is None:
            # 默认生成一个 14x14 的 sin-cos 编码 (224/16 = 14)
            pos_embed = get_2d_sincos_pos_embed(
                embed_dim=self.dim, grid_h=14, grid_w=14,
                device=x.device, dtype=x.dtype
            )  # [1,196,D]
        pos_embed = pos_embed.repeat(n, 1, 1) # [B, 196, D]

        # Calculate dynamic row heights based on the importance PDF.
        row_heights = pdf_to_row_heights(pdf, H / down_ratio, target_h=target_h)
        # Resample positional embeddings and the PDF according to the new dynamic row heights.
        pos_embed = resample_tokens_by_heights(pos_embed, row_heights)
        ret['row_heights'] = row_heights
        pdf = resample_tokens_by_heights(pdf.unsqueeze(-1), row_heights).squeeze(-1)
        pdf = pdf / pdf.sum(dim=-1,keepdim=True) # Re-normalize the PDF

        ret['reshaped_pdf']=pdf
        # Determine the horizontal boundaries (edges) for new patches from the resampled PDF.
        new_edges = get_edges_from_pdf(pdf, new_seqlen=num_patches)
        # Resample positional embeddings based on the new horizontal edges.
        pos_embed = resample_tokens_by_edges(pos_embed, new_edges)
        ret['pos'] = pos_embed
        
        # Scale edges to the actual image dimensions.
        new_edges = new_edges * x.size(2)*row_heights.size(1) / new_edges[0,-1].item()
        ret['new_edges'] = new_edges
        
        # Sample patches from the image dynamically using the calculated row heights and edges.
        patches = dynamic_image_patch_sample(x, row_heights * down_ratio, new_edges, shape=(self.patch_size,self.patch_size))

        # Project the sampled patches into embedding space and reshape.
        x = self.proj(patches.reshape(n * num_patches, C, self.patch_size, self.patch_size)).view(n, num_patches, self.dim)

        x = self.norm(x)
        ret['x']=x
        
        # return x if not ret_dict else ret
        return (x, pos_embed) if not ret_dict else ret


class DARTAdapter(nn.Module):
    """
    DART适配器：封装DART核心功能，提供统一接口
    
    Args:
        input_size: 输入尺寸 (H, W)
        patch_size: patch大小，默认16
        in_chans: 输入通道数，默认3（RGB）或1（audio）
        embed_dim: 嵌入维度，默认768
        scorer_type: 评分网络类型 "mobilenet_small", "efficientnet_b0"
        high_res: 是否使用高分辨率模式
    """
    
    def __init__(
        self,
        input_size: Union[int, Tuple[int, int]] = 224,
        patch_size: int = 16,
        in_chans: int = 3,
        embed_dim: int = 768,
        scorer_type: str = "mobilenet_small",
        high_res: bool = True,
        **kwargs
    ):
        super().__init__()
        
        # 确保input_size是tuple
        if isinstance(input_size, int):
            input_size = (input_size, input_size)
        
        self.input_size = input_size
        self.patch_size = patch_size
        self.in_chans = in_chans
        self.embed_dim = embed_dim
        self.high_res = high_res
        
        # 选择评分网络（如果DART可用）
        if DART_AVAILABLE:
            if scorer_type == "mobilenet_small":
                spn = MobileNetSmallPred(version='v2')
            elif scorer_type == "efficientnet_b0":
                spn = EfficientNetB0Pred(version='v2')
            else:
                raise ValueError(f"Unsupported scorer_type: {scorer_type}")
        else:
            spn = None
        
        # 初始化DART tokenizer
        if DART_AVAILABLE:
            self.dart_core = DART(
                img_size=max(input_size),  # DART使用单一尺寸
                patch_size=patch_size,
                in_chans=in_chans,
                embed_dim=embed_dim,
                spn=spn,
                high_res=high_res,
                **kwargs
            )
        else:
            # 占位实现
            self.dart_core = DART(
                embed_dim=embed_dim,
                in_chans=in_chans,
                patch_size=patch_size,
                **kwargs
            )
        
        # 计算标准网格的patch数量
        stride = patch_size
        if high_res:
            grid_h = (input_size[0] // 2 - patch_size) // stride + 1
            grid_w = (input_size[1] // 2 - patch_size) // stride + 1
        else:
            grid_h = (input_size[0] - patch_size) // stride + 1
            grid_w = (input_size[1] - patch_size) // stride + 1
            
        self.grid_size = (grid_h, grid_w)
        self.num_patches_default = grid_h * grid_w
    
    def forward(
        self,
        x: torch.Tensor,
        num_patches: Optional[int] = None,
        target_h: Optional[int] = None,
        return_dict: bool = False,
        pos_embed: Optional[torch.Tensor] = None
    ) -> Union[torch.Tensor, Dict]:
        """
        动态tokenizer前向传播
        
        Args:
            x: 输入张量 [B, C, H, W]
            num_patches: 目标patch数量，默认使用标准网格数量
            target_h: 目标行数，用于动态分割
            return_dict: 是否返回详细信息字典
            pos_embed: 位置嵌入（可选）
            
        Returns:
            patches: 动态采样的patch嵌入 [B, N, D]
            或包含详细信息的字典（当return_dict=True时）
        """
        B, C, H, W = x.shape
        
        # 参数默认值
        if num_patches is None:
            num_patches = self.num_patches_default
        if target_h is None:
            target_h = self.grid_size[0]
        
        # 调用DART核心
        if DART_AVAILABLE:
            result = self.dart_core(
                x,
                ret_dict=return_dict,
                pos_embed=pos_embed,
                target_h=target_h,
                num_patches=num_patches
            )
        else:
            # 占位实现：返回简化结果
            tokens = self.dart_core(x)
            # 扩展到目标token数量
            if tokens.size(1) < num_patches:
                tokens = tokens.repeat(1, num_patches, 1)[:, :num_patches]
            elif tokens.size(1) > num_patches:
                tokens = tokens[:, :num_patches]
                
            if return_dict:
                result = {
                    'x': tokens,
                    'input_shape': (B, C, H, W),
                    'num_patches': num_patches,
                    'target_h': target_h,
                    'grid_size': self.grid_size
                }
            else:
                result = tokens
        
        if return_dict:
            # 添加额外的元信息
            result['input_shape'] = (B, C, H, W) 
            result['num_patches'] = num_patches
            result['target_h'] = target_h
            result['grid_size'] = self.grid_size
            
        return result
    
    def get_pos_encoding(self, patches: torch.Tensor, pos_grid: torch.Tensor) -> torch.Tensor:
        """
        从位置网格生成位置编码
        
        Args:
            patches: patch嵌入 [B, N, D]
            pos_grid: 位置坐标 [B, N, 2] 
            
        Returns:
            pos_encoding: 位置编码 [B, N, D]
        """
        B, N, D = patches.shape
        
        # 简单的正弦位置编码
        pos_x = pos_grid[:, :, 0]  # [B, N]
        pos_y = pos_grid[:, :, 1]  # [B, N]
        
        # 生成频率
        freqs = torch.arange(0, D // 4, dtype=torch.float32, device=patches.device)
        freqs = freqs / (D // 4)
        freqs = 10000 ** (-freqs)  # [D//4]
        
        # 计算位置编码
        pos_x = pos_x.unsqueeze(-1) * freqs.unsqueeze(0).unsqueeze(0)  # [B, N, D//4]
        pos_y = pos_y.unsqueeze(-1) * freqs.unsqueeze(0).unsqueeze(0)  # [B, N, D//4]
        
        # 交错排列sin/cos
        pos_enc = torch.zeros(B, N, D, device=patches.device)
        pos_enc[:, :, 0::4] = torch.sin(pos_x)
        pos_enc[:, :, 1::4] = torch.cos(pos_x)  
        pos_enc[:, :, 2::4] = torch.sin(pos_y)
        pos_enc[:, :, 3::4] = torch.cos(pos_y)
        
        return pos_enc


class AudioDARTAdapter(DARTAdapter):
    """
    音频专用DART适配器
    
    专门处理log-mel谱图的动态tokenization
    输入格式: [B, 1, T, F] 其中T=时间帧数，F=mel bins数
    """
    
    def __init__(
        self,
        input_size: Tuple[int, int] = (64, 224),  # (T, F) 默认64帧x224mel_bins  
        patch_size: int = 16,
        embed_dim: int = 768,
        scorer_type: str = "mobilenet_small",
        **kwargs
    ):
        super().__init__(
            input_size=input_size,
            patch_size=patch_size,
            in_chans=1,  # 音频为单通道
            embed_dim=embed_dim,
            scorer_type=scorer_type,
            high_res=False,  # 音频通常不需要高分辨率
            **kwargs
        )
    
    def forward(
        self,
        x: torch.Tensor,
        num_patches: Optional[int] = None,
        target_h: Optional[int] = None,
        return_dict: bool = False,
        pos_embed: Optional[torch.Tensor] = None
    ) -> Union[torch.Tensor, Dict]:
        """
        音频动态tokenizer前向传播
        
        Args:
            x: log-mel谱图 [B, 1, T, F]
            其他参数同父类
            
        Returns:
            同父类
        """
        # 验证输入格式
        assert x.dim() == 4 and x.size(1) == 1, f"Expected [B,1,T,F], got {x.shape}"
        
        return super().forward(x, num_patches, target_h, return_dict, pos_embed)


class VisualDARTAdapter(DARTAdapter):
    """
    视觉专用DART适配器
    
    专门处理图像/视频帧的动态tokenization
    支持RGB/灰度图像
    """
    
    def __init__(
        self,
        input_size: Union[int, Tuple[int, int]] = 224,
        patch_size: int = 16,
        in_chans: int = 3,  # RGB
        embed_dim: int = 768,
        scorer_type: str = "mobilenet_small",
        high_res: bool = True,
        **kwargs
    ):
        super().__init__(
            input_size=input_size,
            patch_size=patch_size,
            in_chans=in_chans,
            embed_dim=embed_dim,
            scorer_type=scorer_type,
            high_res=high_res,
            **kwargs
        )
    
    def forward(
        self,
        x: torch.Tensor,
        num_patches: Optional[int] = None,
        target_h: Optional[int] = None,
        return_dict: bool = False,
        pos_embed: Optional[torch.Tensor] = None
    ) -> Union[torch.Tensor, Dict]:
        """
        视觉动态tokenizer前向传播
        
        Args:
            x: 图像张量 [B, C, H, W]
            其他参数同父类
            
        Returns:
            同父类
        """
        # 验证输入格式
        assert x.dim() == 4, f"Expected 4D tensor [B,C,H,W], got {x.shape}"
        assert x.size(1) == self.in_chans, f"Expected {self.in_chans} channels, got {x.size(1)}"
        
        return super().forward(x, num_patches, target_h, return_dict, pos_embed)


# 便利函数
def create_visual_dart_adapter(input_size=224, embed_dim=768, **kwargs):
    """创建视觉DART适配器的便利函数"""
    return VisualDARTAdapter(
        input_size=input_size,
        embed_dim=embed_dim,
        **kwargs
    )


def create_audio_dart_adapter(input_size=(64, 224), embed_dim=768, **kwargs):
    """创建音频DART适配器的便利函数"""
    return AudioDARTAdapter(
        input_size=input_size,
        embed_dim=embed_dim,
        **kwargs
    )

"""
DART Adapter: 为兼容性创建的DART适配器
"""

import torch
import torch.nn as nn
import torch.nn.functional as F


class DART(nn.Module):
    """
    DART适配器 - 简化版DART实现
    主要用于向后兼容
    """
    
    def __init__(self, input_size=224, patch_size=16, embed_dim=768, 
                 num_patches=196, scorer_type='mobilenet_small'):
        super().__init__()
        
        self.input_size = input_size
        self.patch_size = patch_size
        self.embed_dim = embed_dim
        self.num_patches = num_patches
        
        # 简单的patch投影
        self.patch_embed = nn.Conv2d(3, embed_dim, kernel_size=patch_size, stride=patch_size)
        
        # 位置编码
        self.pos_embed = nn.Parameter(torch.randn(1, num_patches, embed_dim) * 0.02)
        
    def forward(self, x, num_patches=None):
        """
        前向传播
        
        Args:
            x: [B, 3, H, W] - 输入图像
            num_patches: int - patch数量
            
        Returns:
            patches: [B, num_patches, embed_dim] - patch特征
            pos_embed: [B, num_patches, embed_dim] - 位置编码
        """
        if num_patches is None:
            num_patches = self.num_patches
            
        B, C, H, W = x.shape
        
        # 确保输入尺寸
        if H != self.input_size or W != self.input_size:
            x = F.interpolate(x, size=(self.input_size, self.input_size), 
                            mode='bilinear', align_corners=False)
        
        # Patch嵌入
        patches = self.patch_embed(x)  # [B, embed_dim, H', W']
        patches = patches.flatten(2).transpose(1, 2)  # [B, num_patches, embed_dim]
        
        # 如果需要调整patch数量
        if patches.size(1) != num_patches:
            if patches.size(1) > num_patches:
                # 截取
                patches = patches[:, :num_patches]
            else:
                # 填充（重复最后一个patch）
                last_patch = patches[:, -1:].repeat(1, num_patches - patches.size(1), 1)
                patches = torch.cat([patches, last_patch], dim=1)
        
        # 位置编码
        pos_embed = self.pos_embed[:, :num_patches].expand(B, -1, -1)
        
        return patches, pos_embed
