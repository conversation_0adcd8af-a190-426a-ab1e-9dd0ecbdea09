try:
    from mmcv.utils import Registry
except ImportError:
    # 新版本mmcv的导入路径
    try:
        from mmcv import Registry
    except ImportError:
        # 如果mmcv不可用，使用简单的注册器实现
        class Registry:
            def __init__(self, name):
                self.name = name
                self._module_dict = {}
            
            def register_module(self, module=None, name=None):
                if module is not None:
                    module_name = name or module.__name__
                    self._module_dict[module_name] = module
                return module
            
            def get(self, name):
                return self._module_dict.get(name)

# 注册全局对象，通过调用register_module()完成模块的注册
OBJECT_REGISTRY = Registry("VSP_ObJECT_REGISTRY")

DATASET_REGISTRY = Registry("DATASET_REGISTRY")

# 注册AV-DART模型
from DART.av_dart.models.av_dart_model import AVDARTModel
from DART.av_dart.models.av_dart_diffusion_wrapper import AVDARTDiffusionWrapper
OBJECT_REGISTRY.register_module(module=AVDARTModel)
OBJECT_REGISTRY.register_module(module=AVDARTDiffusionWrapper)
