batch_size=4
gpu_ids=0,1
gpu_num=2
n_threads=4
lr=1e-4
n_epochs=10
dt="av_data"
eval_flag=0

# 支持选择模型类型: original, av_dart, 或 enhanced_av_dart
model_type=${1:-"original"}  # 默认使用原始模型

experiment_name=audio_visual
base_path='experiments_on_av_data/'$experiment_name

# 根据模型类型选择配置文件
if [ "$model_type" = "enhanced_av_dart" ]; then
    config_file="cfgs/enhanced_av_dart.py"
    experiment_name="enhanced_av_dart_audio_visual"
    echo "使用Enhanced AV-DART升级版模型训练（包含所有优化组件）"
elif [ "$model_type" = "av_dart" ]; then
    config_file="cfgs/av_dart.py"
    experiment_name="av_dart_audio_visual"
    echo "使用AV-DART基础模型训练"
else
    config_file="cfgs/audio_visual.py"
    echo "使用原始模型训练"
fi

base_path='experiments_on_av_data/'$experiment_name


export PYTHONWARNINGS="ignore"

CUDA_VISIBLE_DEVICES=$gpu_ids python -m torch.distributed.run --nproc_per_node=$gpu_num  train_av_data.py \
    --multiprocessing_distributed \
    --config cfgs/diffusion.yml \
    --gpu_devices $gpu_ids \
    --gpu 1 \
    --data_type $dt \
    --config_file $config_file \
    --batch_size $batch_size \
    --n_threads $n_threads \
    --lr_scheduler MultiStepLR \
    --name $experiment_name \
    --root_path ${base_path} \
    --pretrain_path dhf1k/weights/best.pth \
    --train