"""
Enhanced Audio-Visual Tokenizer with Real DART Integration
使用真实DART和简化音频投影的增强型音视频Tokenizer
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
import math
import sys
import os
from DART.av_dart.adapters.dart_adapter import DART
from .pyramid_dart import PyramidDART
from ..models.av_fusion_block import AVFusionBlock


class EnhancedAVTokenizer(nn.Module):
    """AV Tokenizer，集成真实DART动态tokenization，支持多种token分配策略"""
    
    def __init__(self, embed_dim=768, total_tokens=196, visual_size=224, audio_size=(64, 224), 
                 use_real_dart=True, token_strategy="energy_based", **strategy_kwargs):
        super().__init__()
        self.embed_dim = embed_dim
        self.total_tokens = total_tokens
        self.visual_size = visual_size
        self.audio_size = audio_size
        self.use_real_dart = use_real_dart
        self.token_strategy = token_strategy
        
        # 策略参数
        self.visual_ratio = strategy_kwargs.get('visual_ratio', 0.7)
        self.energy_weight = strategy_kwargs.get('energy_weight', 0.5)
        self.min_visual_ratio = strategy_kwargs.get('min_visual_ratio', 0.5)
        self.max_visual_ratio = strategy_kwargs.get('max_visual_ratio', 0.8)
        self.use_real_dart = strategy_kwargs.get('use_real_dart', False)
        
        # 初始化分配
        self.visual_tokens = int(total_tokens * self.visual_ratio)
        self.audio_tokens = total_tokens - self.visual_tokens
        
        # 选择使用Pyramid DART还是原始DART
        self.use_pyramid_dart = strategy_kwargs.get('use_pyramid_dart', False)
        
        if self.use_pyramid_dart:
            # 使用Pyramid DART多尺度tokenizer
            self.visual_dart = PyramidDART(
                embed_dim=embed_dim,
                k_total=self.visual_tokens,
                input_channels=3,
                use_gumbel=strategy_kwargs.get('use_gumbel', False)
            )
        else:
            # 使用原始DART
            dart_config = {
                'input_size': visual_size,
                'patch_size': 16,
                'embed_dim': embed_dim,
                'num_patches': self.visual_tokens,
                'scorer_type': 'mobilenet_small'
            }
            self.visual_dart = DART(**dart_config)
        
        
        # 音频分支 - 分块投影（移除重复token的低效做法）
        self.audio_chunk_proj = None  # 延迟初始化
        self.audio_target_dim = embed_dim
        self.global_chunk_dim = None  # 锁定全局chunk维度
        
        # 音频能量提取器（用于动态分配） - 恢复原始神经网络结构
        self.audio_energy_extractor = None  # 延迟初始化，根据实际输入维度确定
        
        # 跨模态融合：使用AVFusionBlock替换原有的单次cross-attention
        self.av_fusion_blocks = nn.ModuleList([
            AVFusionBlock(dim=embed_dim, num_heads=8, dropout=0.1)
            for _ in range(strategy_kwargs.get('num_fusion_layers', 2))
        ])
        
        # 保留原有的cross_modal_attention作为backup
        self.cross_modal_attention = nn.MultiheadAttention(
            embed_dim=embed_dim,
            num_heads=8,
            dropout=0.1,
            batch_first=True
        )
        
        # 层归一化
        self.norm = nn.LayerNorm(embed_dim)
    
    def _get_2d_sincos_pos_embed(self, num_patches, embed_dim, device):
        """
        生成2D sin-cos位置编码
        Args:
            num_patches: patch数量
            embed_dim: 嵌入维度
            device: 设备
        Returns:
            pos_embed: [num_patches, embed_dim]
        """
        grid_size = int(num_patches ** 0.5)
        if grid_size * grid_size != num_patches:
            # 如果不是完全平方数，使用近似的矩形网格
            grid_h = int(math.sqrt(num_patches))
            grid_w = (num_patches + grid_h - 1) // grid_h
        else:
            grid_h = grid_w = grid_size
        
        # 生成网格坐标
        grid_h_coords = torch.arange(grid_h, dtype=torch.float32, device=device)
        grid_w_coords = torch.arange(grid_w, dtype=torch.float32, device=device)
        grid = torch.meshgrid(grid_h_coords, grid_w_coords, indexing='ij')
        grid = torch.stack(grid, dim=0)  # [2, grid_h, grid_w]
        
        # 展平网格
        grid = grid.reshape(2, -1)[:, :num_patches]  # [2, num_patches]
        
        # 生成sin-cos编码
        omega = torch.arange(embed_dim // 4, dtype=torch.float32, device=device)
        omega = 1. / (10000 ** (omega / (embed_dim // 4)))
        
        # 对每个坐标维度生成编码
        pos_embed = torch.zeros(num_patches, embed_dim, device=device)
        
        # H维度编码 (前半部分)
        h_coords = grid[0]  # [num_patches]
        h_pos = h_coords.unsqueeze(1) * omega.unsqueeze(0)  # [num_patches, embed_dim//4]
        pos_embed[:, 0:embed_dim//4] = torch.sin(h_pos)
        pos_embed[:, embed_dim//4:embed_dim//2] = torch.cos(h_pos)
        
        # W维度编码 (后半部分)
        w_coords = grid[1]  # [num_patches]
        w_pos = w_coords.unsqueeze(1) * omega.unsqueeze(0)  # [num_patches, embed_dim//4]
        pos_embed[:, embed_dim//2:3*embed_dim//4] = torch.sin(w_pos)
        pos_embed[:, 3*embed_dim//4:embed_dim] = torch.cos(w_pos)
        
        return pos_embed
        
    def extract_audio_energy(self, audio_input):
        """提取音频能量特征 - 使用神经网络结构"""
        import torch.nn.functional as F
        # 处理不同的输入维度格式 - 保持空间结构
        if audio_input.dim() == 3:  # [B, T, F] - 1D频谱
            audio_input = audio_input.unsqueeze(1)  # [B, 1, T, F]
        elif audio_input.dim() == 5:  # [B, C, T, H, W] - 2D频谱图
            # 保持时间和空间结构，使用2D池化
            B, C, T, H, W = audio_input.shape
            # 重新组织为 [B*T, C, H, W] 以便2D池化
            audio_input = audio_input.view(B*T, C, H, W)
            # 使用2D全局平均池化提取能量
            energy_per_frame = F.adaptive_avg_pool2d(audio_input, (1, 1)).view(B*T, C)
            # 重新整形为 [B, T, C] 并在时间维度求平均
            energy_per_frame = energy_per_frame.view(B, T, C).mean(dim=1)  # [B, C]
            return torch.sigmoid(energy_per_frame.mean(dim=1, keepdim=True))  # [B, 1]
        
        # 延迟初始化音频能量提取器，根据实际输入维度
        if self.audio_energy_extractor is None:
            # 获取经过AdaptiveAvgPool2d后的特征维度
            with torch.no_grad():
                # 模拟池化操作以确定输出维度
                if audio_input.dim() != 4:
                    raise ValueError(f"音频输入应为4维 [B, C, T, F]，实际为{audio_input.dim()}维: {audio_input.shape}")
                B, C, T, F = audio_input.shape
                pool_output = torch.nn.functional.adaptive_avg_pool2d(audio_input, (1, 1))
                flattened_dim = pool_output.view(B, -1).size(1)  # C*1*1 = C
                
            # 创建音频能量提取器
            self.audio_energy_extractor = nn.Sequential(
                nn.AdaptiveAvgPool2d((1, 1)),
                nn.Flatten(),
                nn.Linear(flattened_dim, 1),
                nn.Sigmoid()
            ).to(audio_input.device)
        
        # 提取能量特征
        energy = self.audio_energy_extractor(audio_input)  # [B, 1]
        return energy
    
    def _compute_visual_complexity(self, visual_input):
        """计算视觉复杂度（图像熵的近似）"""
        # visual_input: [B, C, H, W] 或 [B, T, C, H, W]
        if visual_input.dim() == 5:
            B, T = visual_input.shape[:2]
            visual_input = visual_input.view(B*T, *visual_input.shape[2:])
            merge_time = True
        else:
            B = visual_input.size(0)
            merge_time = False
            T = 1  # 默认值，避免未绑定错误
            
        # 计算空间梯度作为复杂度指标
        if visual_input.size(1) > 1:
            # 转为灰度
            gray = visual_input.mean(dim=1, keepdim=True)
        else:
            gray = visual_input
            
        # Sobel算子计算梯度
        sobel_x = torch.tensor([[-1, 0, 1], [-2, 0, 2], [-1, 0, 1]], dtype=gray.dtype, device=gray.device)
        sobel_y = torch.tensor([[-1, -2, -1], [0, 0, 0], [1, 2, 1]], dtype=gray.dtype, device=gray.device)
        
        sobel_x = sobel_x.view(1, 1, 3, 3)
        sobel_y = sobel_y.view(1, 1, 3, 3)
        
        # 计算梯度幅值
        grad_x = torch.nn.functional.conv2d(gray, sobel_x, padding=1)
        grad_y = torch.nn.functional.conv2d(gray, sobel_y, padding=1)
        gradient_magnitude = torch.sqrt(grad_x**2 + grad_y**2 + 1e-8)
        
        # 复杂度 = 梯度幅值的平均
        complexity = gradient_magnitude.mean(dim=[1, 2, 3], keepdim=True)  # [B, 1] or [B*T, 1]
        
        if merge_time:
            # 时间维度聚合
            complexity = complexity.view(B, T, 1).mean(dim=1)  # [B, 1]
            
        return complexity
    
    def _compute_energy_variance(self, audio_input):
        """计算音频能量的时序变化率"""
        # 处理不同的输入维度格式 - 保持空间结构
        if audio_input.dim() == 3:  # [B, T, F] - 1D频谱
            audio_input = audio_input.unsqueeze(1)  # [B, 1, T, F]
            B, C, T, F = audio_input.shape
            # 计算每帧的能量
            frame_energy = audio_input.mean(dim=[1, 3])  # [B, T]
        elif audio_input.dim() == 5:  # [B, C, T, H, W] - 2D频谱图
            B, C, T, H, W = audio_input.shape
            # 保持空间结构，计算每帧的空间平均能量
            frame_energy = audio_input.mean(dim=[1, 3, 4])  # [B, T]
        else:
            # 4维输入 [B, C, T, F]
            B, C, T, F = audio_input.shape
            frame_energy = audio_input.mean(dim=[1, 3])  # [B, T]
        
        if T <= 1:
            # 无时序信息，返回零方差
            return torch.zeros(B, 1, device=audio_input.device)
        
        # 计算每帧的能量
        frame_energy = audio_input.mean(dim=[1, 3])  # [B, T]
        
        # 计算时序方差作为动态性指标
        energy_variance = torch.var(frame_energy, dim=1, keepdim=True)  # [B, 1]
        
        # 归一化到合理范围
        energy_variance = torch.sigmoid(energy_variance)
        
        return energy_variance
    
    def dynamic_token_allocation(self, visual_input, audio_input):
        """根据策略动态分配token数量 - 改进版本"""
        if self.token_strategy == "fixed":
            return self.visual_tokens, self.audio_tokens
        
        elif self.token_strategy == "energy_based":
            # 改进1: 非线性映射 + 视觉复杂度反馈
            audio_energy = self.extract_audio_energy(audio_input)  # [B, 1]
            
            # 计算视觉复杂度（图像熵的近似）
            batch_size = visual_input.size(0)
            visual_complexity = self._compute_visual_complexity(visual_input)  # [B, 1]
            
            # 改进2: 时序维度考虑 - 计算能量变化率
            energy_variance = self._compute_energy_variance(audio_input)  # [B, 1]
            
            # 改进3: 双分支融合决策
            # 音频权重：基于能量和变化率的非线性映射
            audio_factor = torch.tanh((audio_energy - 0.5) * 2.0) * 0.5  # [-0.5, 0.5]
            energy_dynamics = torch.sigmoid(energy_variance - 0.3) * 0.3  # [0, 0.3]
            audio_influence = audio_factor + energy_dynamics
            
            # 视觉权重：基于复杂度的自适应调整  
            visual_factor = torch.tanh((visual_complexity - 0.5) * 1.5) * 0.3  # [-0.3, 0.3]
            
            # 融合决策：平衡音频和视觉需求
            alpha = 0.6  # 音频权重
            fusion_factor = alpha * audio_influence + (1 - alpha) * visual_factor
            
            # 非线性调整视觉比例
            visual_ratio = self.visual_ratio + fusion_factor.mean().item()
            visual_ratio = max(self.min_visual_ratio, min(self.max_visual_ratio, visual_ratio))
            
            visual_tokens = int(self.total_tokens * visual_ratio)
            audio_tokens = self.total_tokens - visual_tokens
            
            return visual_tokens, audio_tokens
        
        elif self.token_strategy == "dynamic":
            # 改进4: 任务驱动的学习信号 - 可学习的分配函数
            batch_size = visual_input.size(0)
            
            # 提取多模态特征
            audio_energy = self.extract_audio_energy(audio_input)
            visual_complexity = self._compute_visual_complexity(visual_input)
            energy_variance = self._compute_energy_variance(audio_input)
            
            # 创建特征向量 [B, 3]
            features = torch.cat([audio_energy, visual_complexity, energy_variance], dim=1)
            
            # 可学习的分配网络（延迟初始化）
            if not hasattr(self, 'allocation_mlp') or self.allocation_mlp is None:
                self.allocation_mlp = nn.Sequential(
                    nn.Linear(3, 8),
                    nn.ReLU(),
                    nn.Linear(8, 4),
                    nn.ReLU(), 
                    nn.Linear(4, 1),
                    nn.Sigmoid()  # 输出视觉比例 [0, 1]
                ).to(features.device)
            
            # 预测最优视觉比例
            predicted_ratio = self.allocation_mlp(features).mean().item()
            
            # 约束在合理范围内
            visual_ratio = self.min_visual_ratio + predicted_ratio * (self.max_visual_ratio - self.min_visual_ratio)
            
            visual_tokens = int(self.total_tokens * visual_ratio)
            audio_tokens = self.total_tokens - visual_tokens
            
            return visual_tokens, audio_tokens
        
        else:
            return self.visual_tokens, self.audio_tokens

    def process_visual_input(self, visual_input, num_visual_tokens):
        if visual_input.dim() == 5:  # [B, T, C, H, W]
            orig_B, T = visual_input.shape[:2]
            visual_input = visual_input.view(-1, *visual_input.shape[2:])  # [B*T, C, H, W]
        elif visual_input.dim() == 4:  # [B, C, H, W]
            orig_B, T = visual_input.size(0), 1
        else:
            raise ValueError(f"不支持的视觉输入维度: {visual_input.shape}")

        if visual_input.size(1) != 3:
            visual_input = visual_input[:, :3, :, :]

        if visual_input.shape[-2:] != (224, 224):
            visual_input = F.interpolate(
                visual_input, size=(224, 224),
                mode='bilinear', align_corners=False
            )


        if not self.use_real_dart:
            # ✅ Fallback：固定网格切 patch，不走动态采样
            B, C, H, W = visual_input.shape
            patch_size = 16
            patches = visual_input.unfold(2, patch_size, patch_size).unfold(3, patch_size, patch_size)
            patches = patches.contiguous().view(B, -1, patch_size * patch_size * C)
            proj = nn.Linear(patch_size * patch_size * C, self.embed_dim).to(visual_input.device)
            visual_tokens = proj(patches)
            pos_embed = torch.zeros_like(visual_tokens)
            return visual_tokens, pos_embed


        # === 调用 DART ===
        dart_result = self.visual_dart(visual_input, num_patches=num_visual_tokens)

        if isinstance(dart_result, tuple):
            visual_tokens, pos_embed = dart_result
        else:
            visual_tokens, pos_embed = dart_result, None

        # # [B*T, N, D] → [B, T*N, D]
        # if visual_tokens.dim() == 3:
        #     visual_tokens = visual_tokens.view(orig_B, T * num_visual_tokens, self.embed_dim)

        # === [B*T, patches, D] → [B, T*patches, D] 然后进行时间聚合
        if visual_tokens.dim() == 3 and orig_B > 0 and T > 1:
            # 先reshape回原始batch维度
            visual_tokens = visual_tokens.reshape(orig_B, T * visual_tokens.size(1), self.embed_dim)
            
            # 时间维度聚合：将T*patches的tokens重新组织并平均池化
            patches_per_frame = visual_tokens.size(1) // T
            visual_tokens = visual_tokens.view(orig_B, T, patches_per_frame, self.embed_dim)
            visual_tokens = visual_tokens.mean(dim=1)  # [B, patches_per_frame, D]
            
            # 更新pos_embed以匹配聚合后的维度
            if pos_embed is not None and pos_embed.dim() == 3:
                pos_embed = pos_embed.reshape(orig_B, T, patches_per_frame, self.embed_dim)
                pos_embed = pos_embed.mean(dim=1)  # [B, patches_per_frame, D]

        # 位置编码初始化
        if pos_embed is None:
            B, N, D = visual_tokens.shape
            # 使用2D sin-cos位置编码替代全零向量
            pos_embed_2d = self._get_2d_sincos_pos_embed(N, D, visual_tokens.device)  # [N, D]
            pos_embed = pos_embed_2d.unsqueeze(0).expand(B, -1, -1)  # [B, N, D]


        return visual_tokens, pos_embed



    def process_audio_input(self, audio_input, num_audio_tokens):
        """处理音频输入 - 分块投影为多token，避免重复复制"""
        # 统一维度到 [B, T, F]
        if audio_input.dim() == 5:  # [B, 1, T, H, W]
            B, C, T, H, W = audio_input.shape
            audio_input = audio_input.view(B, T, H * W)
        elif audio_input.dim() == 4:  # [B, 1, T, F]
            audio_input = audio_input.squeeze(1)
        elif audio_input.dim() == 3:  # [B, T, F]
            pass
        elif audio_input.dim() == 2:  # [B, F]
            audio_input = audio_input.unsqueeze(1)
        else:
            raise ValueError(f"不支持的音频输入维度: {audio_input.shape}")

        B = audio_input.size(0)

        # 展平时频特征
        audio_flat = audio_input.view(B, -1)  # [B, T*F]

        # 将展平后的向量均匀分块为 num_audio_tokens 份，不足则零填充
        total_dim = audio_flat.size(1)
        
        # 锁定全局chunk_dim以避免频繁重建
        if self.global_chunk_dim is None:
            self.global_chunk_dim = math.ceil(total_dim / max(1, num_audio_tokens))
            
        chunk_dim = self.global_chunk_dim
        pad_len = chunk_dim * num_audio_tokens - total_dim
        if pad_len > 0:
            audio_flat = F.pad(audio_flat, (0, pad_len))
        elif total_dim > chunk_dim * num_audio_tokens:
            # 如果超出，截断到固定长度
            audio_flat = audio_flat[:, :chunk_dim * num_audio_tokens]
            
        audio_chunks = audio_flat.view(B, num_audio_tokens, chunk_dim)  # [B, N_a, C]

        # 创建或复用音频投影层
        if self.audio_chunk_proj is None:
            self.audio_chunk_proj = nn.Linear(chunk_dim, self.audio_target_dim).to(audio_flat.device)


        # 对每个块投影得到 token 表示
        audio_tokens = self.audio_chunk_proj(audio_chunks)  # [B, N_a, D]
        
        return audio_tokens

    def forward(self, visual_input, audio_input):
        # 动态分配
        num_visual_tokens, num_audio_tokens = self.dynamic_token_allocation(
            visual_input, audio_input
        )

        # 视觉分支：取 tokens 和 pos_embed
        visual_tokens, visual_pos = self.process_visual_input(visual_input, num_visual_tokens)
        # 音频分支：只生成 tokens
        audio_tokens = self.process_audio_input(audio_input, num_audio_tokens)

        # batch 维度统一：保证至少 [B, N, D]
        if visual_tokens.dim() == 2:
            visual_tokens = visual_tokens.unsqueeze(0)
        if audio_tokens.dim() == 2:
            audio_tokens = audio_tokens.unsqueeze(0)

        Bv, Nv, D = visual_tokens.shape
        Ba, Na, _ = audio_tokens.shape

        # === 位置编码初始化 ===
        # visual_pos 来自 DART，audio_pos 使用2D sin-cos编码
        if visual_pos is not None:
            visual_pos = visual_pos.view(Bv, Nv, -1)  # 确保三维
        else:
            # 使用2D sin-cos位置编码替代全零向量
            visual_pos_2d = self._get_2d_sincos_pos_embed(Nv, self.embed_dim, visual_tokens.device)
            visual_pos = visual_pos_2d.unsqueeze(0).expand(Bv, -1, -1)
        
        # 音频位置编码也使用2D sin-cos
        audio_pos_2d = self._get_2d_sincos_pos_embed(Na, self.embed_dim, audio_tokens.device)
        audio_pos = audio_pos_2d.unsqueeze(0).expand(Ba, -1, -1)

        # === batch 对齐 ===
        if Bv != Ba:
            if Ba == 1:  # 扩展音频 batch
                audio_tokens = audio_tokens.expand(Bv, -1, -1)
                audio_pos = audio_pos.expand(Bv, -1, -1)
            elif Bv == 1:  # 扩展视觉 batch
                visual_tokens = visual_tokens.expand(Ba, -1, -1)
                visual_pos = visual_pos.expand(Ba, -1, -1)
            elif Bv % Ba == 0:  # 视觉是B*T，音频是B，需要时间聚合
                T = Bv // Ba
                # 将视觉tokens从[B*T, N, D]reshape回[B, T, N, D]
                visual_tokens = visual_tokens.reshape(Ba, T, visual_tokens.size(1), visual_tokens.size(2))
                # 在时间维上做平均池化，得到[B, N, D]
                visual_tokens = visual_tokens.mean(dim=1)
                
                # 同样处理位置编码
                if visual_pos is not None:
                    visual_pos = visual_pos.reshape(Ba, T, visual_pos.size(1), visual_pos.size(2))
                    visual_pos = visual_pos.mean(dim=1)
                
                # 更新Bv以匹配Ba
                Bv = Ba
            else:
                # 处理非整除的边角情况
                raise ValueError(f"Unsupported Bv/Ba ratio: {Bv}/{Ba}. Check dataloader's frame sampling/padding.")
        
        # 添加护栏断言：确保batch对齐成功
        assert visual_tokens.shape[0] == audio_tokens.shape[0], \
            f"After alignment, batch mismatch: visual={visual_tokens.shape}, audio={audio_tokens.shape}"
        
        if visual_pos is not None:
            assert visual_pos.shape[0] == audio_tokens.shape[0], \
                f"Visual pos batch mismatch: visual_pos={visual_pos.shape}, audio={audio_tokens.shape}"

        # === 跨模态增强：使用AVFusionBlock交替式融合 ===
        enhanced_visual = visual_tokens
        enhanced_audio = audio_tokens
        
        # 多层交替式Self-Attn和Cross-Attn
        for fusion_block in self.av_fusion_blocks:
            enhanced_visual, enhanced_audio = fusion_block(
                enhanced_visual, enhanced_audio
            )
        
        # 融合音视频
        av_tokens = torch.cat([enhanced_visual, enhanced_audio], dim=1)
        av_tokens = self.norm(av_tokens)

        # === 位置编码 ===
        av_positions = torch.cat([visual_pos, audio_pos], dim=1)

        return av_tokens, av_positions


def create_enhanced_av_tokenizer(embed_dim=768, total_tokens=196, visual_size=224, 
                                audio_size=(64, 224), token_strategy="dynamic", **kwargs):
    """创建增强型AV Tokenizer的便捷函数"""
    return EnhancedAVTokenizer(
        embed_dim=embed_dim,
        total_tokens=total_tokens, 
        visual_size=visual_size,
        audio_size=audio_size,
        token_strategy=token_strategy,
        **kwargs
    )