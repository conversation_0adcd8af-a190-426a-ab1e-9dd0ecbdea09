import sys
import os
import json

# 添加路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(current_dir)
sys.path.insert(0, project_root)
sys.path.insert(0, current_dir)

import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader, Dataset
import numpy as np
from tqdm import tqdm
import argparse
import time
import math
from collections import defaultdict
from datetime import datetime
import warnings


def avdart_loss(pred_saliency, target_saliency, kl_weight=1.0, cc_weight=1.0):
    from models.sal_losses import kldiv2, cc_s2

    # 修复batch维度不匹配问题
    if pred_saliency.shape[0] != target_saliency.shape[0]:
        if target_saliency.shape[0] == 1 and pred_saliency.shape[0] > 1:
            target_saliency = target_saliency.repeat(pred_saliency.shape[0], 1, 1, 1)

    # 确保 [B,1,H,W]
    if pred_saliency.dim() == 4 and pred_saliency.shape[1] != 1:
        pred_saliency = pred_saliency.mean(dim=1, keepdim=True)
    if target_saliency.dim() == 4 and target_saliency.shape[1] != 1:
        target_saliency = target_saliency.mean(dim=1, keepdim=True)

    # 空间对齐
    if pred_saliency.shape[-2:] != target_saliency.shape[-2:]:
        target_saliency = torch.nn.functional.interpolate(
            target_saliency, size=pred_saliency.shape[-2:],
            mode="bilinear", align_corners=False
        )

    kl_loss = kldiv2(pred_saliency, target_saliency) * kl_weight
    cc_loss = cc_s2(pred_saliency, target_saliency) * cc_weight

    total_loss = kl_loss + cc_loss
    return {"total": total_loss, "kl": kl_loss, "cc": cc_loss}


def avdart_loss_smooth(pred_saliency, target_saliency, kl_weight=1.0, cc_weight=1.0, mse_weight=0.3):
    from models.sal_losses import kldiv2, cc_s2

    # 修复batch维度不匹配问题
    if pred_saliency.shape[0] != target_saliency.shape[0]:
        if target_saliency.shape[0] == 1 and pred_saliency.shape[0] > 1:
            target_saliency = target_saliency.repeat(pred_saliency.shape[0], 1, 1, 1)

    if pred_saliency.dim() == 4 and pred_saliency.shape[1] != 1:
        pred_saliency = pred_saliency.mean(dim=1, keepdim=True)
    if target_saliency.dim() == 4 and target_saliency.shape[1] != 1:
        target_saliency = target_saliency.mean(dim=1, keepdim=True)

    if pred_saliency.shape[-2:] != target_saliency.shape[-2:]:
        target_saliency = torch.nn.functional.interpolate(
            target_saliency, size=pred_saliency.shape[-2:], mode="bilinear", align_corners=False
        )

    kl_loss = kldiv2(pred_saliency, target_saliency) * kl_weight
    cc_loss = cc_s2(pred_saliency, target_saliency) * cc_weight
    mse_loss = torch.nn.functional.mse_loss(pred_saliency, target_saliency) * mse_weight

    total_loss = kl_loss + cc_loss + mse_loss
    return {"total": total_loss, "kl": kl_loss, "cc": cc_loss, "mse": mse_loss}


def avdart_loss_mse(pred_saliency, target_saliency):
    """MSE损失函数 - 遵循DiffSal原始实现"""
    # 修复batch维度不匹配问题
    if pred_saliency.shape[0] != target_saliency.shape[0]:
        if target_saliency.shape[0] == 1 and pred_saliency.shape[0] > 1:
            target_saliency = target_saliency.repeat(pred_saliency.shape[0], 1, 1, 1)
    
    if pred_saliency.shape[-2:] != target_saliency.shape[-2:]:
        target_saliency = torch.nn.functional.interpolate(
            target_saliency, size=pred_saliency.shape[-2:], mode="bilinear", align_corners=False
        )
    
    # 使用DiffSal原始的MSE计算方式：对空间维度求和而非求平均
    mse_loss = (pred_saliency - target_saliency).square().sum(dim=(1,2,3)).mean(dim=0)
    return {"total": mse_loss}


def avdart_loss_complete(pred_saliency, target_saliency, kl_weight=1.0, cc_weight=1.0, sim_weight=1.0, nss_weight=1.0):
    """完整损失函数 - 支持所有评估指标：KL + CC + SIM + NSS"""
    from models.sal_losses import kldiv2, cc_s2, similarity2, nss2
    
    # 修复batch维度不匹配问题
    if pred_saliency.shape[0] != target_saliency.shape[0]:
        if target_saliency.shape[0] == 1 and pred_saliency.shape[0] > 1:
            target_saliency = target_saliency.repeat(pred_saliency.shape[0], 1, 1, 1)

    # 确保 [B,1,H,W]
    if pred_saliency.dim() == 4 and pred_saliency.shape[1] != 1:
        pred_saliency = pred_saliency.mean(dim=1, keepdim=True)
    if target_saliency.dim() == 4 and target_saliency.shape[1] != 1:
        target_saliency = target_saliency.mean(dim=1, keepdim=True)

    # 空间对齐
    if pred_saliency.shape[-2:] != target_saliency.shape[-2:]:
        target_saliency = torch.nn.functional.interpolate(
            target_saliency, size=pred_saliency.shape[-2:],
            mode="bilinear", align_corners=False
        )

    # 计算所有损失项
    kl_loss = kldiv2(pred_saliency, target_saliency) * kl_weight
    cc_loss = cc_s2(pred_saliency, target_saliency) * cc_weight  
    sim_loss = similarity2(pred_saliency, target_saliency) * sim_weight
    nss_loss = nss2(pred_saliency, target_saliency) * nss_weight

    total_loss = kl_loss + cc_loss + sim_loss + nss_loss
    
    return {
        "total": total_loss, 
        "kl": kl_loss, 
        "cc": cc_loss, 
        "sim": sim_loss, 
        "nss": nss_loss
    }
