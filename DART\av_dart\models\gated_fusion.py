"""
Gated Fusion Units: 门控融合单元
替换简单的skip连接，实现更智能的特征融合
"""

import torch
import torch.nn as nn
import torch.nn.functional as F


class GatedFuse(nn.Module):
    """
    门控融合单元
    
    智能地融合上采样特征和跳跃连接特征，避免简单相加导致的信息稀释
    使用SE/CBAM风格的注意力机制进行门控
    """
    
    def __init__(self, cin_skip, cin_up, cout, gate_type="se", reduction=16):
        super().__init__()
        
        self.gate_type = gate_type
        
        # 特征投影
        self.proj_skip = nn.Conv2d(cin_skip, cout, kernel_size=1, bias=False)
        self.proj_up = nn.Conv2d(cin_up, cout, kernel_size=1, bias=False)
        
        # 门控机制
        if gate_type == "se":
            # SE (Squeeze-and-Excitation) 风格门控
            self.gate = SEGate(cout * 2, reduction)
        elif gate_type == "cbam":
            # CBAM 风格门控（通道+空间）
            self.gate = CBAMGate(cout * 2, reduction)
        elif gate_type == "simple":
            # 简单的门控
            self.gate = SimpleGate(cout * 2, cout)
        else:
            raise ValueError(f"Unsupported gate_type: {gate_type}")
        
        # 最终卷积
        self.conv = nn.Sequential(
            nn.Conv2d(cout, cout, kernel_size=3, padding=1, bias=False),
            nn.BatchNorm2d(cout),
            nn.ReLU(inplace=True)
        )
        
        self._init_weights()
    
    def _init_weights(self):
        """初始化权重"""
        for m in self.modules():
            if isinstance(m, nn.Conv2d):
                nn.init.kaiming_normal_(m.weight, mode='fan_out', nonlinearity='relu')
                if m.bias is not None:
                    nn.init.constant_(m.bias, 0)
            elif isinstance(m, nn.BatchNorm2d):
                nn.init.constant_(m.weight, 1)
                nn.init.constant_(m.bias, 0)
    
    def forward(self, up_feat, skip_feat):
        """
        前向传播
        
        Args:
            up_feat: [B, C_up, H, W] - 上采样特征
            skip_feat: [B, C_skip, H, W] - 跳跃连接特征
            
        Returns:
            fused_feat: [B, C_out, H, W] - 融合后特征
        """
        
        # 投影到统一维度
        up_proj = self.proj_up(up_feat)
        skip_proj = self.proj_skip(skip_feat)
        
        # 确保空间尺寸一致
        if up_proj.shape[-2:] != skip_proj.shape[-2:]:
            up_proj = F.interpolate(
                up_proj, size=skip_proj.shape[-2:],
                mode='bilinear', align_corners=False
            )
        
        # 拼接特征用于门控计算
        concat_feat = torch.cat([up_proj, skip_proj], dim=1)
        
        # 计算门控权重
        gate_weights = self.gate(concat_feat)
        
        if self.gate_type in ["se", "simple"]:
            # 通道级门控：分离上采样和跳跃连接的权重
            up_weight, skip_weight = torch.chunk(gate_weights, 2, dim=1)
            fused_feat = up_proj * up_weight + skip_proj * skip_weight
        else:  # cbam
            # CBAM返回的是完整的权重图
            fused_feat = concat_feat * gate_weights
            fused_feat = fused_feat[:, :fused_feat.size(1)//2] + fused_feat[:, fused_feat.size(1)//2:]
        
        # 最终卷积
        fused_feat = self.conv(fused_feat)
        
        return fused_feat


class SEGate(nn.Module):
    """SE (Squeeze-and-Excitation) 门控"""
    
    def __init__(self, channels, reduction=16):
        super().__init__()
        
        self.avg_pool = nn.AdaptiveAvgPool2d(1)
        self.fc = nn.Sequential(
            nn.Linear(channels, channels // reduction, bias=False),
            nn.ReLU(inplace=True),
            nn.Linear(channels // reduction, channels, bias=False),
            nn.Sigmoid()
        )
    
    def forward(self, x):
        """
        Args:
            x: [B, C, H, W] - 输入特征
        Returns:
            gate: [B, C, 1, 1] - 门控权重
        """
        B, C = x.shape[:2]
        
        # 全局平均池化
        y = self.avg_pool(x).view(B, C)
        
        # FC层计算通道权重
        y = self.fc(y).view(B, C, 1, 1)
        
        return y


class CBAMGate(nn.Module):
    """CBAM (Convolutional Block Attention Module) 门控"""
    
    def __init__(self, channels, reduction=16):
        super().__init__()
        
        # 通道注意力
        self.channel_attention = ChannelAttention(channels, reduction)
        
        # 空间注意力
        self.spatial_attention = SpatialAttention()
    
    def forward(self, x):
        """
        Args:
            x: [B, C, H, W] - 输入特征
        Returns:
            gate: [B, C, H, W] - 门控权重
        """
        
        # 通道注意力
        x = x * self.channel_attention(x)
        
        # 空间注意力
        x = x * self.spatial_attention(x)
        
        return x


class ChannelAttention(nn.Module):
    """通道注意力模块"""
    
    def __init__(self, channels, reduction=16):
        super().__init__()
        
        self.avg_pool = nn.AdaptiveAvgPool2d(1)
        self.max_pool = nn.AdaptiveMaxPool2d(1)
        
        self.fc = nn.Sequential(
            nn.Conv2d(channels, channels // reduction, 1, bias=False),
            nn.ReLU(),
            nn.Conv2d(channels // reduction, channels, 1, bias=False)
        )
        
        self.sigmoid = nn.Sigmoid()
    
    def forward(self, x):
        avg_out = self.fc(self.avg_pool(x))
        max_out = self.fc(self.max_pool(x))
        out = avg_out + max_out
        return self.sigmoid(out)


class SpatialAttention(nn.Module):
    """空间注意力模块"""
    
    def __init__(self, kernel_size=7):
        super().__init__()
        
        self.conv = nn.Conv2d(2, 1, kernel_size, padding=kernel_size//2, bias=False)
        self.sigmoid = nn.Sigmoid()
    
    def forward(self, x):
        avg_out = torch.mean(x, dim=1, keepdim=True)
        max_out, _ = torch.max(x, dim=1, keepdim=True)
        x = torch.cat([avg_out, max_out], dim=1)
        x = self.conv(x)
        return self.sigmoid(x)


class SimpleGate(nn.Module):
    """简单门控"""
    
    def __init__(self, cin, cout):
        super().__init__()
        
        self.gate = nn.Sequential(
            nn.Conv2d(cin, cout, kernel_size=1, bias=False),
            nn.ReLU(inplace=True),
            nn.Conv2d(cout, cout, kernel_size=1, bias=False),
            nn.Sigmoid()
        )
    
    def forward(self, x):
        return self.gate(x)


class MultiScaleGatedFuse(nn.Module):
    """
    多尺度门控融合
    
    处理多个尺度的特征融合，每个尺度使用独立的门控机制
    """
    
    def __init__(self, channels_list, out_channels, gate_type="se"):
        super().__init__()
        
        self.num_scales = len(channels_list)
        
        # 每个尺度的投影层
        self.projections = nn.ModuleList([
            nn.Conv2d(c, out_channels, kernel_size=1, bias=False)
            for c in channels_list
        ])
        
        # 门控机制
        total_channels = out_channels * self.num_scales
        if gate_type == "se":
            self.gate = SEGate(total_channels)
        elif gate_type == "cbam":
            self.gate = CBAMGate(total_channels)
        else:
            self.gate = SimpleGate(total_channels, total_channels)
        
        # 最终融合
        self.final_conv = nn.Sequential(
            nn.Conv2d(out_channels, out_channels, kernel_size=3, padding=1, bias=False),
            nn.BatchNorm2d(out_channels),
            nn.ReLU(inplace=True)
        )
    
    def forward(self, feature_list, target_size=None):
        """
        Args:
            feature_list: list of [B, C_i, H_i, W_i] - 多尺度特征
            target_size: (H, W) - 目标尺寸，默认使用最大尺寸
            
        Returns:
            fused_feat: [B, out_channels, H, W] - 融合特征
        """
        
        if target_size is None:
            # 找到最大的空间尺寸
            max_h = max(f.size(2) for f in feature_list)
            max_w = max(f.size(3) for f in feature_list)
            target_size = (max_h, max_w)
        
        # 投影并上采样到统一尺寸
        projected_feats = []
        for feat, proj in zip(feature_list, self.projections):
            proj_feat = proj(feat)
            if proj_feat.shape[-2:] != target_size:
                proj_feat = F.interpolate(
                    proj_feat, size=target_size,
                    mode='bilinear', align_corners=False
                )
            projected_feats.append(proj_feat)
        
        # 拼接所有特征
        concat_feat = torch.cat(projected_feats, dim=1)
        
        # 门控
        gate_weights = self.gate(concat_feat)
        
        # 加权融合
        weighted_feats = concat_feat * gate_weights
        
        # 分组求和
        B, _, H, W = weighted_feats.shape
        out_channels = weighted_feats.size(1) // self.num_scales
        weighted_feats = weighted_feats.view(B, self.num_scales, out_channels, H, W)
        fused_feat = weighted_feats.sum(dim=1)  # [B, out_channels, H, W]
        
        # 最终卷积
        fused_feat = self.final_conv(fused_feat)
        
        return fused_feat


def create_gated_fuse(cin_skip, cin_up, cout, gate_type="se", **kwargs):
    """创建门控融合单元的便捷函数"""
    return GatedFuse(
        cin_skip=cin_skip,
        cin_up=cin_up,
        cout=cout,
        gate_type=gate_type,
        **kwargs
    )


def create_multiscale_gated_fuse(channels_list, out_channels, gate_type="se", **kwargs):
    """创建多尺度门控融合的便捷函数"""
    return MultiScaleGatedFuse(
        channels_list=channels_list,
        out_channels=out_channels,
        gate_type=gate_type,
        **kwargs
    )