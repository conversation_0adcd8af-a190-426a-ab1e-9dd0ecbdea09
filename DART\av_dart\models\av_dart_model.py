"""
AV-DART Model: Audio-Visual Dynamic Adaptive Region Tokenizer Model
完整的AV-DART模型，包含tokenizer → Transformer → 解码器
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
import sys
import os

# 添加路径以支持导入
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(os.path.dirname(os.path.dirname(current_dir)))
sys.path.insert(0, project_root)

from DART.av_dart.models.saliency_decoder import SaliencyDecoder



class AVDARTModel(nn.Module):
    """AV-DART模型，支持多种token分配策略"""
    
    def __init__(
        self,
        embed_dim: int = 768,
        total_tokens: int = 196,
        num_layers: int = 6,
        num_heads: int = 8,
        visual_size: int = 224,
        audio_size: tuple = (64, 224),
        output_size: tuple = (224, 384),  
        token_strategy: str = "energy_based",  # 新增：token分配策略
        **strategy_kwargs  # 新增：策略特定参数
    ):
        super().__init__()
        
        # 延迟导入避免循环依赖
        from DART.av_dart.tokenizers.enhanced_av_tokenizer import EnhancedAVTokenizer
        
        self.embed_dim = embed_dim
        self.total_tokens = total_tokens
        self.output_size = output_size
        
        # AV-DART Tokenizer（传递token分配策略参数）
        self.av_tokenizer = EnhancedAVTokenizer(
            embed_dim=embed_dim,
            total_tokens=total_tokens,
            visual_size=visual_size,
            audio_size=audio_size,
            token_strategy=token_strategy,
            **strategy_kwargs
        )
        
        # CLS token
        self.cls_token = nn.Parameter(torch.randn(1, 1, embed_dim))
        
        # Transformer编码器
        encoder_layer = nn.TransformerEncoderLayer(
            d_model=embed_dim,
            nhead=num_heads,
            dim_feedforward=embed_dim * 4,
            dropout=0.1,
            activation='relu',
            batch_first=True
        )
        self.transformer = nn.TransformerEncoder(
            encoder_layer,
            num_layers=num_layers
        )
        
        # 显著性解码器
        self.saliency_head = SaliencyDecoder(
            embed_dim=embed_dim,
            output_size=output_size,
            hidden_dim=256
        )
        
        
        
        # 参数初始化
        self._init_weights()
    
    def _init_weights(self):
        """初始化模型权重"""
        for m in self.modules():
            if isinstance(m, nn.Linear):
                nn.init.trunc_normal_(m.weight, std=0.02)
                if m.bias is not None:
                    nn.init.constant_(m.bias, 0)
            elif isinstance(m, nn.LayerNorm):
                nn.init.constant_(m.bias, 0)
                nn.init.constant_(m.weight, 1.0)
            elif isinstance(m, nn.Conv2d):
                nn.init.kaiming_normal_(m.weight, mode='fan_out', nonlinearity='relu')
                if m.bias is not None:
                    nn.init.constant_(m.bias, 0)
    
    def forward(self, visual_input, audio_input):
        """前向传播"""
        # 视觉输入：统一到 [B, T, C, H, W]，其余保持，交由 tokenizer 处理
        if visual_input.dim() == 5:
            # 可能是 [B, C, T, H, W]（来自数据集），需要转为 [B, T, C, H, W]
            if visual_input.size(1) in (1, 3) and visual_input.size(2) <= 128:
                visual_input = visual_input.permute(0, 2, 1, 3, 4).contiguous()
        elif visual_input.dim() == 4:
            # [B, C, H, W] 直接交由 tokenizer 处理
            pass
        else:
            raise ValueError(f"不支持的视觉输入维度: {visual_input.shape}")

        # 音频输入：仅规整异常6维，其余交由 tokenizer 处理
        if audio_input.dim() == 6:  # [B, 1, T, C, H, W] - 意外的6维
            audio_input = audio_input.view(audio_input.size(0), *audio_input.shape[2:])
        
        # AV-DART tokenization - 保持原始维度
        av_tokens, av_positions = self.av_tokenizer(visual_input, audio_input)
        
        # av_tokens的形状处理
        if av_tokens.dim() == 3:  # [B, N, D] - 图像模式
            final_B = av_tokens.size(0)
        elif av_tokens.dim() == 4:  # [B, T*N, D] 或 其他格式
            final_B = av_tokens.size(0)
            av_tokens = av_tokens.view(final_B, -1, av_tokens.size(-1))
        else:
            final_B = av_tokens.size(0)
            av_tokens = av_tokens.view(final_B, -1, av_tokens.size(-1))
        
        # 添加CLS token
        cls_tokens = self.cls_token.expand(final_B, -1, -1)
        tokens = torch.cat([cls_tokens, av_tokens], dim=1)
        
        # Transformer编码
        encoded = self.transformer(tokens)
        
        # 分离CLS token和patch tokens
        cls_feat = encoded[:, 0]
        patch_feats = encoded[:, 1:]
        
        # 显著性预测
        saliency_map = self.saliency_head(patch_feats, cls_feat)
        
        return saliency_map
    
    def get_attention_maps(self, visual_input, audio_input):
        """获取注意力图（用于可视化分析）"""
        # 这是一个辅助方法，可以用于分析模型的注意力模式
        self.eval()
        with torch.no_grad():
            # 获取tokens
            av_tokens, _ = self.av_tokenizer(visual_input, audio_input)
            B = av_tokens.size(0)
            
            # 添加CLS token
            cls_tokens = self.cls_token.expand(B, -1, -1)
            tokens = torch.cat([cls_tokens, av_tokens], dim=1)
            
            # 获取每层的注意力权重
            attention_maps = []
            x = tokens
            
            for layer in self.transformer.layers:
                # 获取自注意力权重
                attn_output, attn_weights = layer.self_attn(
                    x, x, x, need_weights=True, average_attn_weights=False
                )
                attention_maps.append(attn_weights)
                
                # 继续前向传播
                x = layer.norm1(x + layer.dropout1(attn_output))
                x = layer.norm2(x + layer.dropout2(layer.linear2(F.relu(layer.linear1(x)))))
            
            return attention_maps
    
    def get_model_complexity(self):
        """获取模型复杂度信息"""
        total_params = sum(p.numel() for p in self.parameters())
        trainable_params = sum(p.numel() for p in self.parameters() if p.requires_grad)
        
        complexity_info = {
            'total_parameters': total_params,
            'trainable_parameters': trainable_params,
            'embed_dim': self.embed_dim,
            'total_tokens': self.total_tokens,
            'transformer_layers': len(self.transformer.layers),
            'output_size': self.output_size
        }
        
        return complexity_info
    
    def set_token_strategy(self, strategy, **kwargs):
        """动态设置token分配策略"""
        self.av_tokenizer.token_strategy = strategy
        for key, value in kwargs.items():
            if hasattr(self.av_tokenizer, key):
                setattr(self.av_tokenizer, key, value)
        
        


def create_av_dart_model(embed_dim=768, total_tokens=196, num_layers=6, num_heads=8,
                        output_size=(224, 384), token_strategy="dynamic", **kwargs):
    """创建AV-DART模型的便捷函数"""
    return AVDARTModel(
        embed_dim=embed_dim,
        total_tokens=total_tokens,
        num_layers=num_layers,
        num_heads=num_heads,
        output_size=output_size,
        token_strategy=token_strategy,
        **kwargs
    )


# 预定义的模型配置
def create_av_dart_small(**kwargs):
    """创建小型AV-DART模型"""
    return create_av_dart_model(
        embed_dim=384,
        total_tokens=144,
        num_layers=4,
        num_heads=6,
        **kwargs
    )


def create_av_dart_base(**kwargs):
    """创建基础AV-DART模型"""
    return create_av_dart_model(
        embed_dim=768,
        total_tokens=196,
        num_layers=6,
        num_heads=8,
        **kwargs
    )


def create_av_dart_large(**kwargs):
    """创建大型AV-DART模型"""
    return create_av_dart_model(
        embed_dim=1024,
        total_tokens=256,
        num_layers=8,
        num_heads=16,
        **kwargs
    )