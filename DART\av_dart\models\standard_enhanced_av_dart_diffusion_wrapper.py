"""
Enhanced AV-DART 扩散模型包装器
使StandardEnhancedAVDARTModel与原始扩散训练框架兼容
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import sys
import os

# 添加项目路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(os.path.dirname(current_dir))
sys.path.insert(0, project_root)

from DART.av_dart.models.standard_enhanced_av_dart_model import StandardEnhancedAVDARTModel


class StandardEnhancedAVDARTDiffusionWrapper(nn.Module):
    """
    Standard Enhanced AV-DART扩散模型包装器
    使模型与原始扩散训练框架兼容
    """
    
    def __init__(self, **model_config):
        super().__init__()
        
        # 创建核心Enhanced AV-DART模型
        self.av_dart_model = StandardEnhancedAVDARTModel(**model_config)
        
        # 模拟原始模型的属性
        self.audio_net = True
        self.spatiotemp_net = None
        
        # 创建虚拟的视觉骨干网络（用于保持接口兼容性）
        self.visual_backbone = self._create_visual_backbone()
        
        # 时间嵌入维度
        self.time_embedding_dim = 128
        self.time_mlp = nn.Sequential(
            nn.Linear(self.time_embedding_dim, model_config.get('embed_dim', 768)),
            nn.ReLU(),
            nn.Linear(model_config.get('embed_dim', 768), model_config.get('embed_dim', 768))
        )
        
        # 时间投影层
        self.time_proj = nn.Linear(model_config.get('embed_dim', 768), 1)
        
    def _create_visual_backbone(self):
        """创建虚拟视觉骨干网络"""
        class VirtualVisualNet(nn.Module):
            def __init__(self):
                super().__init__()
                
            def forward(self, x):
                # 返回4个尺度的特征，匹配原始VideoSaliencyModel的输出格式
                B = x.size(0)
                device = x.device
                
                # 模拟MViT的多尺度输出: [768, 384, 192, 96]通道数，不同空间分辨率
                vis_list = [
                    torch.randn((B, 768, 8, 7, 12), device=device),   # scale 0
                    torch.randn((B, 384, 8, 14, 24), device=device),  # scale 1  
                    torch.randn((B, 192, 8, 28, 48), device=device),  # scale 2
                    torch.randn((B, 96, 8, 56, 96), device=device),   # scale 3
                ]
                return vis_list
                
        return VirtualVisualNet()
        
    def get_timestep_embedding(self, timesteps, embedding_dim):
        """生成时间步嵌入"""
        assert len(timesteps.shape) == 1
        half_dim = embedding_dim // 2
        emb = torch.log(torch.tensor(10000.0)) / (half_dim - 1)
        emb = torch.exp(torch.arange(half_dim, dtype=torch.float32, device=timesteps.device) * -emb)
        emb = timesteps.float()[:, None] * emb[None, :]
        emb = torch.cat([torch.sin(emb), torch.cos(emb)], dim=1)
        if embedding_dim % 2 == 1:  # zero pad
            emb = F.pad(emb, (0, 1))
        return emb
    
    def forward(self, data, t_tensor=None):
        """
        扩散模型训练的前向传播接口
        
        Args:
            data: 字典，包含
                - "img": 图像特征 [B, C, H, W]
                - "input": 噪声输入 [B, C, H, W]  
                - "train_obj": 训练目标 [B, C, H, W]
                - "audio": 音频特征 [B, ...]
            或者直接传入(imgs, audio)元组
            t_tensor: 时间步 [B]，可选
        Returns:
            pred: 预测结果 [B, C, H, W]
        """
        # 检查输入类型
        if isinstance(data, tuple) and len(data) == 2:
            # 直接传入(imgs, audio)的情况
            imgs, audio = data
        elif isinstance(data, dict):
            # 字典形式的输入
            imgs = data["img"]      # 视觉输入 [B, C, H, W]
            audio = data["audio"]   # 音频输入 [B, C, T, F]
            x_noisy = data.get("input")  # 噪声输入（扩散过程的当前状态）
        else:
            raise TypeError(f"不支持的输入类型: {type(data)}")
        
        # 使用Enhanced AV-DART模型直接预测显著性图
        saliency_pred, aux_outputs = self.av_dart_model(imgs, audio)
        
        # 如果是扩散训练模式，需要处理时间嵌入
        if isinstance(data, dict) and "input" in data and t_tensor is not None:
            x_noisy = data["input"]
            # 确保输出维度正确
            if saliency_pred.shape[-2:] != x_noisy.shape[-2:]:
                saliency_pred = F.interpolate(
                    saliency_pred, size=x_noisy.shape[-2:], 
                    mode="bilinear", align_corners=False
                )
            
            # 生成时间嵌入并融合
            time_emb = self.get_timestep_embedding(t_tensor, self.time_embedding_dim)
            time_emb = self.time_mlp(time_emb)  # [B, embed_dim]
            time_emb_proj = self.time_proj(time_emb).unsqueeze(-1).unsqueeze(-1)  # [B, 1, 1, 1]
            
            # 融合时间信息（简单相加）
            saliency_pred_with_time = saliency_pred + 0.1 * time_emb_proj.expand_as(saliency_pred)
            
            return saliency_pred_with_time
        
        # 直接返回显著性预测结果
        return saliency_pred
    
    def forward_vggish(self, audio_input):
        """增强的forward_vggish方法"""
        if audio_input is None:
            B = 4
            device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        else:
            B = audio_input.size(0)
            device = audio_input.device
            
        # 模拟VGGish的输出格式
        bs, T = B, 8
        audio_feat_map = torch.randn(bs, 128, T, 6, 4, device=device)
        return audio_feat_map, audio_feat_map
    
    def visual_net(self, visual_input):
        """视觉网络接口"""
        return self.visual_backbone(visual_input)
    
    def decoder_net(self, x, t, vis_feat, audio_feat_embed=None, **kwargs):
        """
        解码器网络接口 - 直接使用AV-DART模型的输出
        """
        # 由于我们的模型已经完成了完整的前向传播，这里直接返回输入
        # 在实际使用中，扩散训练器会调用完整的forward方法
        return x


def create_standard_enhanced_av_dart_diffusion_wrapper(**kwargs):
    """创建Standard Enhanced AV-DART扩散包装器的便捷函数"""
    return StandardEnhancedAVDARTDiffusionWrapper(**kwargs)