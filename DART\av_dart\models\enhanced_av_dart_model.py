"""
Enhanced AV-DART Model: 升级版音视频显著性模型
整合所有升级组件：Pyramid DART + AVFusion + RoPE + Enhanced Decoder
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
import sys
import os

# 添加路径以支持导入
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(os.path.dirname(os.path.dirname(current_dir)))
sys.path.insert(0, project_root)

from ..tokenizers.pyramid_dart import PyramidDART
from ..tokenizers.enhanced_av_tokenizer import EnhancedAVTokenizer
from .av_fusion_block import AVFusionBlock, GatedFusion
from .rope_attention import RoPETransformer
from .enhanced_saliency_decoder import EnhancedSaliencyDecoder
from ..utils.multi_metric_loss import MultiMetricLoss


class EnhancedAVDARTModel(nn.Module):
    """
    升级版AV-DART模型
    
    架构升级：
    1. Pyramid DART多尺度视觉分词
    2. 交替式AV融合块
    3. RoPE位置编码Transformer
    4. 门控融合解码器 + 中心偏置先验
    5. 多指标损失函数
    """
    
    def __init__(
        self,
        # 基础参数
        embed_dim: int = 768,
        total_tokens: int = 196,
        num_transformer_layers: int = 6,
        num_heads: int = 8,
        visual_size: int = 224,
        audio_size: tuple = (64, 224),
        output_size: tuple = (224, 384),
        
        # Tokenizer参数
        use_pyramid_dart: bool = True,
        use_gumbel_topk: bool = False,
        token_strategy: str = "dynamic",
        
        # 融合参数
        num_fusion_layers: int = 3,
        use_layer_scale: bool = False,
        
        # Transformer参数
        use_rope: bool = True,
        rope_theta: float = 10000.0,
        mlp_ratio: float = 4.0,
        dropout: float = 0.1,
        
        # 解码器参数
        decoder_hidden_dim: int = 256,
        use_gated_fusion: bool = True,
        use_center_bias: bool = True,
        use_multiscale_supervision: bool = True,
        gate_type: str = "se",
        center_bias_type: str = "learnable",
        
        # 损失函数参数
        use_multi_metric_loss: bool = True,
        kl_weight: float = 1.0,
        cc_weight: float = 1.0,
        sim_weight: float = 1.0,
        nss_weight: float = 1.0,
        
        **kwargs
    ):
        super().__init__()
        
        self.embed_dim = embed_dim
        self.total_tokens = total_tokens
        self.output_size = output_size
        self.use_pyramid_dart = use_pyramid_dart
        self.use_rope = use_rope
        self.use_multi_metric_loss = use_multi_metric_loss
        
        # AV Tokenizer
        if use_pyramid_dart:
            # 使用增强型tokenizer + Pyramid DART
            self.av_tokenizer = EnhancedAVTokenizer(
                embed_dim=embed_dim,
                total_tokens=total_tokens,
                visual_size=visual_size,
                audio_size=audio_size,
                token_strategy=token_strategy,
                use_pyramid_dart=True,
                use_gumbel=use_gumbel_topk,
                num_fusion_layers=num_fusion_layers,
                **kwargs
            )
        else:
            # 使用原始增强型tokenizer
            self.av_tokenizer = EnhancedAVTokenizer(
                embed_dim=embed_dim,
                total_tokens=total_tokens,
                visual_size=visual_size,
                audio_size=audio_size,
                token_strategy=token_strategy,
                use_pyramid_dart=False,
                num_fusion_layers=num_fusion_layers,
                **kwargs
            )
        
        # CLS token
        self.cls_token = nn.Parameter(torch.randn(1, 1, embed_dim))
        
        # Transformer编码器
        if use_rope:
            # 使用RoPE Transformer
            self.transformer = RoPETransformer(
                num_layers=num_transformer_layers,
                dim=embed_dim,
                num_heads=num_heads,
                mlp_ratio=mlp_ratio,
                dropout=dropout,
                layer_scale_init_value=1e-5 if use_layer_scale else None,
                rope_theta=rope_theta
            )
        else:
            # 使用标准Transformer
            encoder_layer = nn.TransformerEncoderLayer(
                d_model=embed_dim,
                nhead=num_heads,
                dim_feedforward=int(embed_dim * mlp_ratio),
                dropout=dropout,
                activation='gelu',
                batch_first=True
            )
            self.transformer = nn.TransformerEncoder(
                encoder_layer,
                num_layers=num_transformer_layers
            )
        
        # 显著性解码器
        self.saliency_head = EnhancedSaliencyDecoder(
            embed_dim=embed_dim,
            output_size=output_size,
            hidden_dim=decoder_hidden_dim,
            use_gated_fusion=use_gated_fusion,
            use_center_bias=use_center_bias,
            use_multiscale_supervision=use_multiscale_supervision,
            gate_type=gate_type,
            center_bias_type=center_bias_type
        )
        
        # 损失函数
        if use_multi_metric_loss:
            self.criterion = MultiMetricLoss(
                kl_weight=kl_weight,
                cc_weight=cc_weight,
                sim_weight=sim_weight,
                nss_weight=nss_weight
            )
        else:
            self.criterion = nn.BCELoss()
        
        # 参数初始化
        self._init_weights()
    
    def _init_weights(self):
        """初始化模型权重"""
        for m in self.modules():
            if isinstance(m, nn.Linear):
                nn.init.trunc_normal_(m.weight, std=0.02)
                if m.bias is not None:
                    nn.init.constant_(m.bias, 0)
            elif isinstance(m, nn.LayerNorm):
                nn.init.constant_(m.bias, 0)
                nn.init.constant_(m.weight, 1.0)
            elif isinstance(m, (nn.Conv2d, nn.ConvTranspose2d)):
                nn.init.kaiming_normal_(m.weight, mode='fan_out', nonlinearity='relu')
                if m.bias is not None:
                    nn.init.constant_(m.bias, 0)
    
    def forward(self, visual_input, audio_input, targets=None, fixation_map=None):
        """
        前向传播
        
        Args:
            visual_input: [B, C, H, W] or [B, T, C, H, W] - 视觉输入
            audio_input: [B, T, F] or [B, 1, T, F] - 音频输入
            targets: [B, 1, H, W] - 目标显著性图（训练时）
            fixation_map: [B, 1, H, W] - 注视点图（可选）
            
        Returns:
            outputs: dict - 包含预测结果和损失的字典
        """
        
        # 数据预处理
        visual_input = self._preprocess_visual(visual_input)
        audio_input = self._preprocess_audio(audio_input)
        
        # AV Tokenization
        av_tokens, av_positions = self.av_tokenizer(visual_input, audio_input)
        
        # 处理token维度
        if av_tokens.dim() == 4:
            B, T, N, D = av_tokens.shape
            av_tokens = av_tokens.view(B, T*N, D)
        
        B = av_tokens.size(0)
        
        # 添加CLS token
        cls_tokens = self.cls_token.expand(B, -1, -1)
        tokens = torch.cat([cls_tokens, av_tokens], dim=1)
        
        # Transformer编码
        if self.use_rope:
            # RoPE Transformer
            encoded = self.transformer(tokens)
        else:
            # 标准Transformer
            encoded = self.transformer(tokens)
        
        # 分离CLS token和patch tokens
        cls_feat = encoded[:, 0]
        patch_feats = encoded[:, 1:]
        
        # 显著性预测
        decoder_outputs = self.saliency_head(patch_feats, cls_feat)
        
        # 构建输出字典
        outputs = {
            'saliency_map': decoder_outputs['saliency_map'],
            'cls_features': cls_feat,
            'patch_features': patch_feats
        }
        
        # 添加多尺度输出
        for key, value in decoder_outputs.items():
            if key.startswith('scale_'):
                outputs[key] = value
        
        # 计算损失（训练时）
        if targets is not None:
            if self.use_multi_metric_loss:
                loss_dict = self.criterion(
                    decoder_outputs['saliency_map'], 
                    targets, 
                    fixation_map
                )
                outputs.update(loss_dict)
                
                # 多尺度监督损失
                if hasattr(self.saliency_head, 'use_multiscale_supervision') and \
                   self.saliency_head.use_multiscale_supervision:
                    multiscale_loss = 0.0
                    for key, pred in decoder_outputs.items():
                        if key.startswith('scale_'):
                            scale_weight = 0.5  # 可调节
                            scale_loss_dict = self.criterion(pred, targets, fixation_map)
                            multiscale_loss += scale_weight * scale_loss_dict['total_loss']
                    
                    outputs['multiscale_loss'] = multiscale_loss
                    outputs['total_loss'] = outputs['total_loss'] + multiscale_loss
            else:
                loss = self.criterion(decoder_outputs['saliency_map'], targets)
                outputs['total_loss'] = loss
        
        return outputs
    
    def _preprocess_visual(self, visual_input):
        """预处理视觉输入"""
        if visual_input.dim() == 5:
            # [B, T, C, H, W] 或 [B, C, T, H, W]
            if visual_input.size(1) in (1, 3) and visual_input.size(2) > 10:
                # [B, C, T, H, W] -> [B, T, C, H, W]
                visual_input = visual_input.permute(0, 2, 1, 3, 4).contiguous()
        elif visual_input.dim() == 4:
            # [B, C, H, W] 保持不变
            pass
        else:
            raise ValueError(f"Unsupported visual input shape: {visual_input.shape}")
        
        return visual_input
    
    def _preprocess_audio(self, audio_input):
        """预处理音频输入"""
        if audio_input.dim() == 6:
            # [B, 1, T, C, H, W] -> [B, T, C*H*W]
            audio_input = audio_input.view(audio_input.size(0), *audio_input.shape[2:])
        
        return audio_input
    
    def get_model_statistics(self, visual_input=None, audio_input=None):
        """
        获取模型统计信息
        
        Returns:
            stats: dict - 模型复杂度和特征统计
        """
        total_params = sum(p.numel() for p in self.parameters())
        trainable_params = sum(p.numel() for p in self.parameters() if p.requires_grad)
        
        stats = {
            'total_parameters': total_params,
            'trainable_parameters': trainable_params,
            'embed_dim': self.embed_dim,
            'total_tokens': self.total_tokens,
            'output_size': self.output_size,
            'use_pyramid_dart': self.use_pyramid_dart,
            'use_rope': self.use_rope,
            'use_multi_metric_loss': self.use_multi_metric_loss
        }
        
        # 如果提供了输入，获取Pyramid DART的尺度统计
        if visual_input is not None and self.use_pyramid_dart:
            try:
                dart_stats = self.av_tokenizer.visual_dart.get_scale_statistics(visual_input)
                stats['pyramid_dart_stats'] = dart_stats
            except:
                pass
        
        # 中心偏置参数
        if hasattr(self.saliency_head, 'get_center_bias_parameters'):
            bias_params = self.saliency_head.get_center_bias_parameters()
            if bias_params:
                stats['center_bias_params'] = bias_params
        
        return stats
    
    def set_token_strategy(self, strategy, **kwargs):
        """动态设置token分配策略"""
        if hasattr(self.av_tokenizer, 'set_token_strategy'):
            self.av_tokenizer.set_token_strategy(strategy, **kwargs)
    
    def visualize_center_bias(self, save_path=None):
        """可视化中心偏置"""
        if hasattr(self.saliency_head, 'visualize_center_bias'):
            return self.saliency_head.visualize_center_bias(save_path)
        else:
            print("Center bias visualization not available")
            return None


def create_enhanced_av_dart_model(**kwargs):
    """创建增强版AV-DART模型的便捷函数"""
    return EnhancedAVDARTModel(**kwargs)


# 预定义配置
def create_enhanced_av_dart_small(**kwargs):
    """小型增强版AV-DART模型"""
    return create_enhanced_av_dart_model(
        embed_dim=384,
        total_tokens=144,
        num_transformer_layers=4,
        num_heads=6,
        decoder_hidden_dim=128,
        num_fusion_layers=2,
        **kwargs
    )


def create_enhanced_av_dart_base(**kwargs):
    """基础增强版AV-DART模型"""
    return create_enhanced_av_dart_model(
        embed_dim=768,
        total_tokens=196,
        num_transformer_layers=6,
        num_heads=8,
        decoder_hidden_dim=256,
        num_fusion_layers=3,
        **kwargs
    )


def create_enhanced_av_dart_large(**kwargs):
    """大型增强版AV-DART模型"""
    return create_enhanced_av_dart_model(
        embed_dim=1024,
        total_tokens=256,
        num_transformer_layers=8,
        num_heads=16,
        decoder_hidden_dim=512,
        num_fusion_layers=4,
        **kwargs
    )


def create_production_av_dart_model(**kwargs):
    """生产环境AV-DART模型（平衡性能和效率）"""
    return create_enhanced_av_dart_model(
        embed_dim=768,
        total_tokens=196,
        num_transformer_layers=6,
        num_heads=8,
        use_pyramid_dart=True,
        use_gumbel_topk=False,
        token_strategy="dynamic",
        num_fusion_layers=3,
        use_rope=True,
        use_gated_fusion=True,
        use_center_bias=True,
        use_multiscale_supervision=True,
        gate_type="se",
        center_bias_type="learnable",
        use_multi_metric_loss=True,
        kl_weight=1.0,
        cc_weight=1.0,
        sim_weight=1.0,
        nss_weight=1.0,
        **kwargs
    )