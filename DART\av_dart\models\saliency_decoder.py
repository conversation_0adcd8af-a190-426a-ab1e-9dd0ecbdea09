"""
多尺度监督显著性解码器
支持中心偏置先验和多尺度监督学习
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import math


class SaliencyDecoder(nn.Module):
    """
    显著性解码器 - 支持多尺度监督和中心偏置先验
    """
    
    def __init__(
        self,
        embed_dim: int = 768,
        output_size: tuple = (224, 384),
        hidden_dim: int = 256,
        use_multiscale: bool = True,
        use_center_bias: bool = True,
        center_bias_type: str = "learnable",  # learnable, gaussian, fixed
        num_upsampling_layers: int = 4
    ):
        super().__init__()
        
        self.embed_dim = embed_dim
        self.output_size = output_size
        self.hidden_dim = hidden_dim
        self.use_multiscale = use_multiscale
        self.use_center_bias = use_center_bias
        self.center_bias_type = center_bias_type
        
        # Token到空间特征的注意力池化
        self.spatial_attention_pool = SpatialAttentionPooling(
            embed_dim=embed_dim,
            hidden_dim=hidden_dim,
            output_size=output_size
        )
        
        # 上采样解码器
        self.decoder_layers = nn.ModuleList()
        current_dim = hidden_dim
        
        for i in range(num_upsampling_layers):
            # 逐步减少通道数，增加空间分辨率
            next_dim = max(current_dim // 2, 64)
            
            self.decoder_layers.append(nn.Sequential(
                nn.ConvTranspose2d(current_dim, next_dim, kernel_size=4, stride=2, padding=1),
                nn.BatchNorm2d(next_dim),
                nn.ReLU(inplace=True),
                nn.Conv2d(next_dim, next_dim, kernel_size=3, padding=1),
                nn.BatchNorm2d(next_dim),
                nn.ReLU(inplace=True)
            ))
            current_dim = next_dim
        
        # 最终预测层
        self.final_conv = nn.Conv2d(current_dim, 1, kernel_size=1)
        
        # 多尺度监督头
        if use_multiscale:
            self.multiscale_heads = nn.ModuleList([
                nn.Conv2d(current_dim, 1, kernel_size=1)
                for current_dim in [hidden_dim // (2**i) for i in range(num_upsampling_layers)]
            ])
        
        # 中心偏置先验
        if use_center_bias:
            self.center_bias = self._create_center_bias(center_bias_type, output_size)
    
    def _create_center_bias(self, bias_type, output_size):
        """创建中心偏置先验"""
        H, W = output_size
        
        if bias_type == "gaussian":
            # 高斯中心偏置
            y, x = torch.meshgrid(
                torch.linspace(-1, 1, H),
                torch.linspace(-1, 1, W),
                indexing='ij'
            )
            gaussian_bias = torch.exp(-(x**2 + y**2) / 0.5)
            gaussian_bias = gaussian_bias / gaussian_bias.max()
            return nn.Parameter(gaussian_bias.unsqueeze(0).unsqueeze(0), requires_grad=False)
        
        elif bias_type == "learnable":
            # 可学习的中心偏置
            bias = torch.zeros(1, 1, H, W)
            nn.init.normal_(bias, mean=0.0, std=0.1)
            return nn.Parameter(bias, requires_grad=True)
        
        elif bias_type == "fixed":
            # 固定的中心偏置（简单的距离场）
            y, x = torch.meshgrid(
                torch.arange(H, dtype=torch.float32),
                torch.arange(W, dtype=torch.float32),
                indexing='ij'
            )
            center_y, center_x = H // 2, W // 2
            distance = torch.sqrt((x - center_x)**2 + (y - center_y)**2)
            max_distance = math.sqrt(center_x**2 + center_y**2)
            fixed_bias = 1.0 - (distance / max_distance)
            return nn.Parameter(fixed_bias.unsqueeze(0).unsqueeze(0), requires_grad=False)
        
        else:
            raise ValueError(f"不支持的中心偏置类型: {bias_type}")
    
    def forward(self, patch_tokens, cls_token=None):
        """
        前向传播
        Args:
            patch_tokens: [B, N, D] patch tokens
            cls_token: [B, D] 全局CLS token (可选)
        Returns:
            outputs: 字典，包含主要预测和多尺度预测
        """
        # 使用注意力池化将tokens转换为空间特征图
        spatial_features = self.spatial_attention_pool(patch_tokens, cls_token)
        
        # 解码过程
        multiscale_preds = []
        x = spatial_features
        
        for i, decoder_layer in enumerate(self.decoder_layers):
            x = decoder_layer(x)
            
            # 多尺度监督
            if self.use_multiscale and i < len(self.multiscale_heads):
                scale_pred = self.multiscale_heads[i](x)
                scale_pred = F.interpolate(
                    scale_pred, size=self.output_size,
                    mode='bilinear', align_corners=False
                )
                multiscale_preds.append(scale_pred)
        
        # 最终预测
        main_pred = self.final_conv(x)
        main_pred = F.interpolate(
            main_pred, size=self.output_size,
            mode='bilinear', align_corners=False
        )
        
        # 应用中心偏置
        if self.use_center_bias:
            center_bias = self.center_bias.expand_as(main_pred)
            main_pred = main_pred + 0.1 * center_bias  # 权重可调
        
        # 组织输出
        outputs = {
            'main': main_pred
        }
        
        if self.use_multiscale:
            outputs['multiscale_preds'] = multiscale_preds
        
        return outputs


class SpatialAttentionPooling(nn.Module):
    """
    空间注意力池化 - 将tokens转换为空间特征图
    """
    
    def __init__(
        self,
        embed_dim: int = 768,
        hidden_dim: int = 256,
        output_size: tuple = (224, 384),
        num_attention_heads: int = 8
    ):
        super().__init__()
        
        self.embed_dim = embed_dim
        self.hidden_dim = hidden_dim
        self.output_size = output_size
        
        # 空间查询生成器
        H, W = output_size
        init_size = (H // 16, W // 16)  # 初始空间分辨率
        self.spatial_queries = nn.Parameter(
            torch.randn(1, hidden_dim, init_size[0], init_size[1])
        )
        
        # Token投影
        self.token_proj = nn.Linear(embed_dim, hidden_dim)
        
        # 跨注意力机制 (Spatial Queries attend to Tokens)
        self.cross_attention = nn.MultiheadAttention(
            embed_dim=hidden_dim,
            num_heads=num_attention_heads,
            dropout=0.1,
            batch_first=True
        )
        
        # 输出投影
        self.output_proj = nn.Conv2d(hidden_dim, hidden_dim, kernel_size=1)
        self.norm = nn.LayerNorm(hidden_dim)
        
    def forward(self, tokens, cls_token=None):
        """
        前向传播
        Args:
            tokens: [B, N, D] patch tokens
            cls_token: [B, D] CLS token (可选)
        Returns:
            spatial_features: [B, hidden_dim, H', W'] 空间特征图
        """
        B, N, D = tokens.shape
        
        # 投影tokens到hidden_dim
        tokens_proj = self.token_proj(tokens)  # [B, N, hidden_dim]
        
        # 如果有CLS token，也加入
        if cls_token is not None:
            cls_proj = self.token_proj(cls_token).unsqueeze(1)  # [B, 1, hidden_dim]
            tokens_proj = torch.cat([cls_proj, tokens_proj], dim=1)  # [B, N+1, hidden_dim]
        
        # 准备空间查询
        H_init, W_init = self.spatial_queries.shape[-2:]
        spatial_queries = self.spatial_queries.expand(B, -1, -1, -1)  # [B, hidden_dim, H', W']
        spatial_queries_flat = spatial_queries.view(B, self.hidden_dim, -1).transpose(1, 2)  # [B, H'*W', hidden_dim]
        
        # 跨注意力: 空间查询 attend to tokens
        attended_features, _ = self.cross_attention(
            query=spatial_queries_flat,    # [B, H'*W', hidden_dim]
            key=tokens_proj,               # [B, N, hidden_dim] 
            value=tokens_proj              # [B, N, hidden_dim]
        )
        
        # 重新组织为空间特征图
        attended_features = attended_features.transpose(1, 2).view(B, self.hidden_dim, H_init, W_init)
        
        # 应用输出投影和归一化
        spatial_features = self.output_proj(attended_features)
        
        # LayerNorm (需要调整维度)
        B, C, H, W = spatial_features.shape
        spatial_features = spatial_features.view(B, C, -1).transpose(1, 2)  # [B, H*W, C]
        spatial_features = self.norm(spatial_features)
        spatial_features = spatial_features.transpose(1, 2).view(B, C, H, W)  # [B, C, H, W]
        
        return spatial_features


class AdaptiveUpsampling(nn.Module):
    """
    自适应上采样模块 - 根据内容调整上采样策略
    """
    
    def __init__(
        self,
        in_channels: int,
        out_channels: int,
        scale_factor: int = 2,
        use_content_aware: bool = True
    ):
        super().__init__()
        
        self.scale_factor = scale_factor
        self.use_content_aware = use_content_aware
        
        # 标准上采样路径
        self.standard_upsample = nn.Sequential(
            nn.ConvTranspose2d(in_channels, out_channels, kernel_size=4, stride=2, padding=1),
            nn.BatchNorm2d(out_channels),
            nn.ReLU(inplace=True)
        )
        
        # 内容感知上采样路径
        if use_content_aware:
            self.content_aware_upsample = nn.Sequential(
                nn.Conv2d(in_channels, out_channels * (scale_factor ** 2), kernel_size=3, padding=1),
                nn.PixelShuffle(scale_factor),
                nn.BatchNorm2d(out_channels),
                nn.ReLU(inplace=True)
            )
            
            # 路径选择权重
            self.path_weight = nn.Sequential(
                nn.AdaptiveAvgPool2d(1),
                nn.Conv2d(in_channels, in_channels // 8, kernel_size=1),
                nn.ReLU(inplace=True),
                nn.Conv2d(in_channels // 8, 1, kernel_size=1),
                nn.Sigmoid()
            )
    
    def forward(self, x):
        """
        前向传播
        Args:
            x: [B, C, H, W] 输入特征
        Returns:
            output: [B, C', H*2, W*2] 上采样特征
        """
        if not self.use_content_aware:
            return self.standard_upsample(x)
        
        # 标准上采样
        standard_out = self.standard_upsample(x)
        
        # 内容感知上采样
        content_out = self.content_aware_upsample(x)
        
        # 计算路径权重
        weight = self.path_weight(x)  # [B, 1, 1, 1]
        
        # 加权融合
        output = weight * content_out + (1 - weight) * standard_out
        
        return output


def create_saliency_decoder(
    embed_dim=768,
    output_size=(224, 384),
    hidden_dim=256,
    use_multiscale=True,
    use_center_bias=True,
    **kwargs
):
    """创建显著性解码器的便捷函数"""
    return SaliencyDecoder(
        embed_dim=embed_dim,
        output_size=output_size,
        hidden_dim=hidden_dim,
        use_multiscale=use_multiscale,
        use_center_bias=use_center_bias,
        **kwargs
    )


def create_lightweight_decoder(
    embed_dim=384,
    output_size=(224, 384),
    hidden_dim=128,
    **kwargs
):
    """创建轻量级显著性解码器"""
    return SaliencyDecoder(
        embed_dim=embed_dim,
        output_size=output_size,
        hidden_dim=hidden_dim,
        use_multiscale=False,
        use_center_bias=False,
        **kwargs
    )


def create_enhanced_decoder(
    embed_dim=768,
    output_size=(224, 384),
    hidden_dim=512,
    **kwargs
):
    """创建增强版显著性解码器"""
    return SaliencyDecoder(
        embed_dim=embed_dim,
        output_size=output_size,
        hidden_dim=hidden_dim,
        use_multiscale=True,
        use_center_bias=True,
        num_upsampling_layers=6,
        **kwargs
    )


def create_high_res_decoder(
    embed_dim=768,
    output_size=(448, 768),
    hidden_dim=256,
    **kwargs
):
    """创建高分辨率显著性解码器"""
    return SaliencyDecoder(
        embed_dim=embed_dim,
        output_size=output_size,
        hidden_dim=hidden_dim,
        use_multiscale=True,
        use_center_bias=True,
        **kwargs
    )