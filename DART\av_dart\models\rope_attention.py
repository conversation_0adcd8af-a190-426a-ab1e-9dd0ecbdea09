"""
RoPE (Rotary Position Embedding) for Enhanced Attention
旋转位置编码，提升对空间位置的建模能力
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import math


class RoPEAttention(nn.Module):
    """
    带RoPE位置编码的多头注意力
    
    相比绝对位置编码，RoPE能更好地捕捉相对位置关系，
    对显著性检测这种强空间任务特别有效
    """
    
    def __init__(self, dim, num_heads=8, dropout=0.1, rope_theta=10000.0, max_seq_len=2048):
        super().__init__()
        
        self.dim = dim
        self.num_heads = num_heads
        self.head_dim = dim // num_heads
        self.dropout = dropout
        self.rope_theta = rope_theta
        self.max_seq_len = max_seq_len
        
        assert dim % num_heads == 0, "dim must be divisible by num_heads"
        
        # Q, K, V投影
        self.q_proj = nn.Linear(dim, dim, bias=False)
        self.k_proj = nn.Linear(dim, dim, bias=False)
        self.v_proj = nn.Linear(dim, dim, bias=False)
        self.out_proj = nn.Linear(dim, dim)
        
        # Dropout
        self.attn_dropout = nn.Dropout(dropout)
        self.proj_dropout = nn.Dropout(dropout)
        
        # 预计算RoPE频率
        self.register_buffer('freqs_cis', self._precompute_freqs_cis())
        
        self._init_weights()
    
    def _init_weights(self):
        """初始化权重"""
        for module in [self.q_proj, self.k_proj, self.v_proj]:
            nn.init.xavier_uniform_(module.weight)
        nn.init.xavier_uniform_(self.out_proj.weight)
        nn.init.constant_(self.out_proj.bias, 0)
    
    def _precompute_freqs_cis(self):
        """
        预计算RoPE频率
        
        Returns:
            freqs_cis: [max_seq_len, head_dim//2] - 复数形式的频率
        """
        # 计算频率
        freqs = 1.0 / (self.rope_theta ** (torch.arange(0, self.head_dim, 2).float() / self.head_dim))
        
        # 生成位置索引
        t = torch.arange(self.max_seq_len, dtype=torch.float)
        
        # 外积得到所有位置-频率组合
        freqs = torch.outer(t, freqs)  # [max_seq_len, head_dim//2]
        
        # 转换为复数形式
        freqs_cis = torch.polar(torch.ones_like(freqs), freqs)  # e^(i*theta)
        
        return freqs_cis
    
    def _apply_rope(self, x, freqs_cis):
        """
        应用RoPE到输入张量
        
        Args:
            x: [B, seq_len, num_heads, head_dim] - 输入张量
            freqs_cis: [seq_len, head_dim//2] - 频率
            
        Returns:
            x_rotated: [B, seq_len, num_heads, head_dim] - 旋转后的张量
        """
        # 将实数张量转换为复数形式
        x_complex = torch.view_as_complex(x.reshape(*x.shape[:-1], -1, 2))  # [B, seq_len, num_heads, head_dim//2]
        
        # 确保freqs_cis维度匹配
        seq_len = x.size(1)
        freqs_cis = freqs_cis[:seq_len]  # [seq_len, head_dim//2]
        
        # 广播并应用旋转
        freqs_cis = freqs_cis.unsqueeze(0).unsqueeze(2)  # [1, seq_len, 1, head_dim//2]
        x_rotated = x_complex * freqs_cis  # 复数乘法实现旋转
        
        # 转换回实数形式
        x_rotated = torch.view_as_real(x_rotated).flatten(-2)  # [B, seq_len, num_heads, head_dim]
        
        return x_rotated
    
    def forward(self, query, key, value, attn_mask=None, key_padding_mask=None, need_weights=False):
        """
        前向传播
        
        Args:
            query: [B, seq_len_q, dim] - 查询
            key: [B, seq_len_k, dim] - 键
            value: [B, seq_len_v, dim] - 值
            attn_mask: 注意力掩码（可选）
            key_padding_mask: 键填充掩码（可选）
            need_weights: 是否返回注意力权重
            
        Returns:
            output: [B, seq_len_q, dim] - 输出
            attn_weights: [B, num_heads, seq_len_q, seq_len_k] - 注意力权重（可选）
        """
        B, seq_len_q, _ = query.shape
        seq_len_k = key.size(1)
        
        # Q, K, V投影
        q = self.q_proj(query)  # [B, seq_len_q, dim]
        k = self.k_proj(key)    # [B, seq_len_k, dim]
        v = self.v_proj(value)  # [B, seq_len_v, dim]
        
        # 重塑为多头形式
        q = q.view(B, seq_len_q, self.num_heads, self.head_dim)  # [B, seq_len_q, num_heads, head_dim]
        k = k.view(B, seq_len_k, self.num_heads, self.head_dim)  # [B, seq_len_k, num_heads, head_dim]
        v = v.view(B, seq_len_k, self.num_heads, self.head_dim)  # [B, seq_len_v, num_heads, head_dim]
        
        # 应用RoPE位置编码
        q = self._apply_rope(q, self.freqs_cis)
        k = self._apply_rope(k, self.freqs_cis)
        
        # 转置为[B, num_heads, seq_len, head_dim]格式
        q = q.transpose(1, 2)  # [B, num_heads, seq_len_q, head_dim]
        k = k.transpose(1, 2)  # [B, num_heads, seq_len_k, head_dim]
        v = v.transpose(1, 2)  # [B, num_heads, seq_len_v, head_dim]
        
        # 计算注意力分数
        scores = torch.matmul(q, k.transpose(-2, -1)) / math.sqrt(self.head_dim)  # [B, num_heads, seq_len_q, seq_len_k]
        
        # 应用注意力掩码
        if attn_mask is not None:
            scores = scores + attn_mask
        
        # 应用键填充掩码
        if key_padding_mask is not None:
            scores = scores.masked_fill(key_padding_mask.unsqueeze(1).unsqueeze(2), float('-inf'))
        
        # Softmax
        attn_weights = F.softmax(scores, dim=-1)  # [B, num_heads, seq_len_q, seq_len_k]
        attn_weights = self.attn_dropout(attn_weights)
        
        # 应用注意力权重
        out = torch.matmul(attn_weights, v)  # [B, num_heads, seq_len_q, head_dim]
        
        # 重塑和投影
        out = out.transpose(1, 2).contiguous().view(B, seq_len_q, self.dim)  # [B, seq_len_q, dim]
        out = self.out_proj(out)
        out = self.proj_dropout(out)
        
        if need_weights:
            return out, attn_weights.mean(dim=1)  # 平均所有头的注意力权重
        else:
            return out, None


class RoPETransformerLayer(nn.Module):
    """
    带RoPE的Transformer层
    
    结合RoPE注意力和标准FFN
    """
    
    def __init__(self, dim, num_heads=8, mlp_ratio=4.0, dropout=0.1, 
                 layer_scale_init_value=None, rope_theta=10000.0):
        super().__init__()
        
        self.norm1 = nn.LayerNorm(dim)
        self.attn = RoPEAttention(
            dim=dim,
            num_heads=num_heads,
            dropout=dropout,
            rope_theta=rope_theta
        )
        
        self.norm2 = nn.LayerNorm(dim)
        mlp_hidden_dim = int(dim * mlp_ratio)
        self.mlp = nn.Sequential(
            nn.Linear(dim, mlp_hidden_dim),
            nn.GELU(),
            nn.Dropout(dropout),
            nn.Linear(mlp_hidden_dim, dim),
            nn.Dropout(dropout)
        )
        
        # Layer Scale (可选)
        self.layer_scale_init_value = layer_scale_init_value
        if layer_scale_init_value is not None:
            self.gamma1 = nn.Parameter(torch.ones(dim) * layer_scale_init_value)
            self.gamma2 = nn.Parameter(torch.ones(dim) * layer_scale_init_value)
    
    def forward(self, x, attn_mask=None, key_padding_mask=None):
        """
        前向传播
        
        Args:
            x: [B, seq_len, dim] - 输入
            attn_mask: 注意力掩码（可选）
            key_padding_mask: 键填充掩码（可选）
            
        Returns:
            x: [B, seq_len, dim] - 输出
        """
        # Self-Attention
        attn_out, _ = self.attn(
            query=self.norm1(x),
            key=self.norm1(x),
            value=self.norm1(x),
            attn_mask=attn_mask,
            key_padding_mask=key_padding_mask
        )
        
        if self.layer_scale_init_value is not None:
            x = x + self.gamma1 * attn_out
        else:
            x = x + attn_out
        
        # FFN
        mlp_out = self.mlp(self.norm2(x))
        
        if self.layer_scale_init_value is not None:
            x = x + self.gamma2 * mlp_out
        else:
            x = x + mlp_out
        
        return x


class RoPETransformer(nn.Module):
    """
    带RoPE的Transformer编码器
    
    堆叠多个RoPETransformerLayer
    """
    
    def __init__(self, num_layers=6, dim=768, num_heads=8, mlp_ratio=4.0, 
                 dropout=0.1, layer_scale_init_value=None, rope_theta=10000.0):
        super().__init__()
        
        self.layers = nn.ModuleList([
            RoPETransformerLayer(
                dim=dim,
                num_heads=num_heads,
                mlp_ratio=mlp_ratio,
                dropout=dropout,
                layer_scale_init_value=layer_scale_init_value,
                rope_theta=rope_theta
            ) for _ in range(num_layers)
        ])
        
        self.norm = nn.LayerNorm(dim)
    
    def forward(self, x, attn_mask=None, key_padding_mask=None):
        """
        前向传播
        
        Args:
            x: [B, seq_len, dim] - 输入
            attn_mask: 注意力掩码（可选）
            key_padding_mask: 键填充掩码（可选）
            
        Returns:
            x: [B, seq_len, dim] - 输出
        """
        for layer in self.layers:
            x = layer(x, attn_mask, key_padding_mask)
        
        x = self.norm(x)
        return x


def create_rope_attention(dim=768, num_heads=8, dropout=0.1, **kwargs):
    """创建RoPE注意力的便捷函数"""
    return RoPEAttention(
        dim=dim,
        num_heads=num_heads,
        dropout=dropout,
        **kwargs
    )


def create_rope_transformer(num_layers=6, dim=768, num_heads=8, **kwargs):
    """创建RoPE Transformer的便捷函数"""
    return RoPETransformer(
        num_layers=num_layers,
        dim=dim,
        num_heads=num_heads,
        **kwargs
    )


# 预定义配置
def create_rope_transformer_small(**kwargs):
    """小型RoPE Transformer"""
    return create_rope_transformer(
        num_layers=4,
        dim=384,
        num_heads=6,
        **kwargs
    )


def create_rope_transformer_base(**kwargs):
    """基础RoPE Transformer"""
    return create_rope_transformer(
        num_layers=6,
        dim=768,
        num_heads=8,
        **kwargs
    )


def create_rope_transformer_large(**kwargs):
    """大型RoPE Transformer"""
    return create_rope_transformer(
        num_layers=8,
        dim=1024,
        num_heads=16,
        **kwargs
    )