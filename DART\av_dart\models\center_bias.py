"""
Center Bias Prior: 中心偏置先验
可学习的2D高斯混合先验，提升显著性预测的中心偏置建模
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import math
import numpy as np


class GaussianMixtureCenterBias(nn.Module):
    """
    可学习的2D高斯混合中心偏置
    
    使用多个高斯分布的混合来建模中心偏置，
    参数可学习，能够适应不同数据集的中心偏置模式
    """
    
    def __init__(self, output_size=(224, 384), num_gaussians=3, learnable=True, 
                 init_centers=None, init_sigmas=None, init_weights=None):
        super().__init__()
        
        self.output_size = output_size
        self.num_gaussians = num_gaussians
        self.learnable = learnable
        self.H, self.W = output_size
        
        # 初始化高斯参数
        if init_centers is None:
            # 默认在图像中心附近初始化
            init_centers = self._default_centers()
        
        if init_sigmas is None:
            # 默认标准差（相对于图像尺寸）
            init_sigmas = self._default_sigmas()
        
        if init_weights is None:
            # 默认权重（均匀分布）
            init_weights = torch.ones(num_gaussians) / num_gaussians
        
        if learnable:
            # 可学习参数
            self.centers = nn.Parameter(init_centers)  # [num_gaussians, 2]
            self.log_sigmas = nn.Parameter(torch.log(init_sigmas))  # [num_gaussians, 2] 
            self.logit_weights = nn.Parameter(torch.logit(init_weights))  # [num_gaussians]
        else:
            # 固定参数
            self.register_buffer('centers', init_centers)
            self.register_buffer('log_sigmas', torch.log(init_sigmas))
            self.register_buffer('logit_weights', torch.logit(init_weights))
        
        # 预计算坐标网格
        self.register_buffer('coord_grid', self._create_coord_grid())
    
    def _default_centers(self):
        """默认的高斯中心位置"""
        if self.num_gaussians == 1:
            # 单个高斯：图像中心
            centers = torch.tensor([[0.5, 0.5]])
        elif self.num_gaussians == 3:
            # 三个高斯：中心 + 左右偏移
            centers = torch.tensor([
                [0.5, 0.5],    # 中心
                [0.4, 0.5],    # 左偏
                [0.6, 0.5]     # 右偏
            ])
        elif self.num_gaussians == 5:
            # 五个高斯：中心 + 四个方向
            centers = torch.tensor([
                [0.5, 0.5],    # 中心
                [0.4, 0.5],    # 左
                [0.6, 0.5],    # 右
                [0.5, 0.4],    # 上
                [0.5, 0.6]     # 下
            ])
        else:
            # 随机初始化，但倾向于中心区域
            centers = torch.randn(self.num_gaussians, 2) * 0.1 + 0.5
            centers = torch.clamp(centers, 0.1, 0.9)
        
        return centers.float()
    
    def _default_sigmas(self):
        """默认的高斯标准差"""
        # 基于图像尺寸的相对标准差
        sigma_h = self.H / 8.0  # 高度的1/8
        sigma_w = self.W / 8.0  # 宽度的1/8
        
        sigmas = torch.tensor([[sigma_h, sigma_w]]).repeat(self.num_gaussians, 1)
        
        return sigmas.float()
    
    def _create_coord_grid(self):
        """创建坐标网格"""
        # 归一化坐标 [0, 1]
        y_coords = torch.linspace(0, 1, self.H)
        x_coords = torch.linspace(0, 1, self.W)
        
        # 创建网格
        grid_y, grid_x = torch.meshgrid(y_coords, x_coords, indexing='ij')
        
        # 堆叠为 [H, W, 2] 格式
        coord_grid = torch.stack([grid_y, grid_x], dim=-1)  # [H, W, 2]
        
        return coord_grid
    
    def forward(self, batch_size=1):
        """
        前向传播，生成中心偏置图
        
        Args:
            batch_size: int - 批次大小
            
        Returns:
            bias_map: [B, 1, H, W] - 中心偏置图
        """
        device = self.centers.device
        
        # 获取当前参数
        centers = self.centers  # [num_gaussians, 2]
        sigmas = torch.exp(self.log_sigmas)  # [num_gaussians, 2]
        weights = torch.softmax(self.logit_weights, dim=0)  # [num_gaussians]
        
        # 坐标网格 [H, W, 2]
        coords = self.coord_grid.to(device)
        
        # 计算每个高斯分布的概率密度
        bias_maps = []
        for i in range(self.num_gaussians):
            center = centers[i:i+1, :]  # [1, 2]
            sigma = sigmas[i:i+1, :]    # [1, 2]
            weight = weights[i]         # scalar
            
            # 计算距离的平方（马哈拉诺比斯距离）
            diff = coords - center.unsqueeze(0).unsqueeze(0)  # [H, W, 2]
            
            # 考虑各向异性（不同维度不同标准差）
            normalized_diff = diff / (sigma.unsqueeze(0).unsqueeze(0) + 1e-8)  # [H, W, 2]
            dist_sq = (normalized_diff ** 2).sum(dim=-1)  # [H, W]
            
            # 高斯概率密度（省略归一化常数）
            gaussian = torch.exp(-0.5 * dist_sq)  # [H, W]
            
            # 加权
            weighted_gaussian = weight * gaussian
            bias_maps.append(weighted_gaussian)
        
        # 混合所有高斯分布
        bias_map = torch.stack(bias_maps, dim=0).sum(dim=0)  # [H, W]
        
        # 归一化到 [0, 1]
        bias_map = bias_map / (bias_map.max() + 1e-8)
        
        # 扩展批次维度
        bias_map = bias_map.unsqueeze(0).unsqueeze(0).repeat(batch_size, 1, 1, 1)  # [B, 1, H, W]
        
        return bias_map
    
    def get_parameters_dict(self):
        """获取当前参数的字典形式（用于分析）"""
        with torch.no_grad():
            centers = self.centers.cpu()
            sigmas = torch.exp(self.log_sigmas).cpu()
            weights = torch.softmax(self.logit_weights, dim=0).cpu()
            
            params_dict = {
                'centers': centers.numpy(),
                'sigmas': sigmas.numpy(), 
                'weights': weights.numpy(),
                'num_gaussians': self.num_gaussians,
                'output_size': self.output_size
            }
            
            return params_dict
    
    def visualize_bias(self, save_path=None):
        """可视化中心偏置图"""
        self.eval()
        with torch.no_grad():
            bias_map = self.forward(batch_size=1)[0, 0]  # [H, W]
            
            # 转换为numpy用于可视化
            bias_np = bias_map.cpu().numpy()
            
            if save_path:
                try:
                    import matplotlib.pyplot as plt
                    
                    plt.figure(figsize=(10, 6))
                    plt.imshow(bias_np, cmap='hot', interpolation='bilinear')
                    plt.colorbar(label='Bias Strength')
                    plt.title(f'Center Bias Map ({self.num_gaussians} Gaussians)')
                    plt.axis('off')
                    plt.tight_layout()
                    plt.savefig(save_path, dpi=150, bbox_inches='tight')
                    plt.close()
                    
                    print(f"Bias map saved to {save_path}")
                except ImportError:
                    print("Warning: matplotlib not available, visualization skipped")
            
            return bias_np


class DatasetAdaptiveCenterBias(nn.Module):
    """
    数据集自适应的中心偏置
    
    根据数据集特性自动调整中心偏置参数
    """
    
    def __init__(self, output_size=(224, 384), dataset_type="general"):
        super().__init__()
        
        self.output_size = output_size
        self.dataset_type = dataset_type
        
        # 根据数据集类型设置不同的初始参数
        if dataset_type == "mit1003":
            # MIT1003: 强中心偏置
            self.bias_generator = GaussianMixtureCenterBias(
                output_size=output_size,
                num_gaussians=1,
                learnable=True
            )
        elif dataset_type == "salicon":
            # SALICON: 中等中心偏置，多峰
            self.bias_generator = GaussianMixtureCenterBias(
                output_size=output_size,
                num_gaussians=3,
                learnable=True
            )
        elif dataset_type == "dhf1k":
            # DHF1K: 视频数据，较弱中心偏置
            centers = torch.tensor([[0.5, 0.5], [0.4, 0.4], [0.6, 0.6]])
            sigmas = torch.tensor([[output_size[0]/6, output_size[1]/6]]).repeat(3, 1)
            weights = torch.tensor([0.6, 0.2, 0.2])
            
            self.bias_generator = GaussianMixtureCenterBias(
                output_size=output_size,
                num_gaussians=3,
                learnable=True,
                init_centers=centers,
                init_sigmas=sigmas,
                init_weights=weights
            )
        else:
            # 通用设置
            self.bias_generator = GaussianMixtureCenterBias(
                output_size=output_size,
                num_gaussians=3,
                learnable=True
            )
    
    def forward(self, batch_size=1):
        return self.bias_generator(batch_size)


class FixedCenterBias(nn.Module):
    """
    固定的中心偏置（基于经验分布）
    
    使用预定义的中心偏置模式，不进行学习
    """
    
    def __init__(self, output_size=(224, 384), bias_type="gaussian"):
        super().__init__()
        
        self.output_size = output_size
        self.bias_type = bias_type
        
        # 预计算偏置图
        bias_map = self._create_fixed_bias()
        self.register_buffer('bias_map', bias_map)
    
    def _create_fixed_bias(self):
        """创建固定的偏置图"""
        H, W = self.output_size
        
        if self.bias_type == "gaussian":
            # 高斯中心偏置
            y_center, x_center = H // 2, W // 2
            sigma_y, sigma_x = H // 4, W // 4
            
            y_coords = torch.arange(H).float()
            x_coords = torch.arange(W).float()
            
            y_grid, x_grid = torch.meshgrid(y_coords, x_coords, indexing='ij')
            
            dist_sq = ((y_grid - y_center) / sigma_y) ** 2 + ((x_grid - x_center) / sigma_x) ** 2
            bias_map = torch.exp(-0.5 * dist_sq)
            
        elif self.bias_type == "uniform":
            # 均匀分布（无偏置）
            bias_map = torch.ones(H, W)
            
        elif self.bias_type == "center_rectangle":
            # 矩形中心偏置
            bias_map = torch.zeros(H, W)
            h_start, h_end = H // 4, 3 * H // 4
            w_start, w_end = W // 4, 3 * W // 4
            bias_map[h_start:h_end, w_start:w_end] = 1.0
            
        else:
            raise ValueError(f"Unknown bias_type: {self.bias_type}")
        
        # 归一化
        bias_map = bias_map / (bias_map.max() + 1e-8)
        
        return bias_map.unsqueeze(0).unsqueeze(0)  # [1, 1, H, W]
    
    def forward(self, batch_size=1):
        """
        前向传播
        
        Args:
            batch_size: int - 批次大小
            
        Returns:
            bias_map: [B, 1, H, W] - 中心偏置图
        """
        return self.bias_map.repeat(batch_size, 1, 1, 1)


def create_center_bias(output_size=(224, 384), bias_type="learnable", **kwargs):
    """创建中心偏置的便捷函数"""
    if bias_type == "learnable":
        return GaussianMixtureCenterBias(output_size=output_size, **kwargs)
    elif bias_type == "adaptive":
        return DatasetAdaptiveCenterBias(output_size=output_size, **kwargs)
    elif bias_type == "fixed":
        return FixedCenterBias(output_size=output_size, **kwargs)
    else:
        raise ValueError(f"Unknown bias_type: {bias_type}")


# 预定义配置
def create_mit1003_center_bias(output_size=(224, 384)):
    """MIT1003数据集的中心偏置"""
    return DatasetAdaptiveCenterBias(output_size=output_size, dataset_type="mit1003")


def create_salicon_center_bias(output_size=(224, 384)):
    """SALICON数据集的中心偏置"""
    return DatasetAdaptiveCenterBias(output_size=output_size, dataset_type="salicon")


def create_dhf1k_center_bias(output_size=(224, 384)):
    """DHF1K数据集的中心偏置"""
    return DatasetAdaptiveCenterBias(output_size=output_size, dataset_type="dhf1k")


def create_no_center_bias(output_size=(224, 384)):
    """无中心偏置（均匀分布）"""
    return FixedCenterBias(output_size=output_size, bias_type="uniform")