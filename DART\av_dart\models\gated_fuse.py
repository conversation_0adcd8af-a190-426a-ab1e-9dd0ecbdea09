"""
GatedFuse: 门控融合模块
用于替代简单的skip连接，提供更好的特征融合和边界质量
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import math
from typing import Optional, Tuple, List


class GatedFuse(nn.Module):
    """
    门控融合模块，用于智能地融合上采样特征和跳跃连接特征
    通过学习的门控机制决定两种特征的权重分配
    """
    
    def __init__(
        self,
        cin_skip: int,
        cin_up: int,
        cout: int,
        gate_type: str = "attention",  # "attention", "sigmoid", "softmax", "se"
        activation: str = "relu",      # "relu", "gelu", "swish"
        use_spatial_attention: bool = True,
        use_channel_attention: bool = True,
        dropout: float = 0.1
    ):
        super().__init__()
        
        self.cin_skip = cin_skip
        self.cin_up = cin_up
        self.cout = cout
        self.gate_type = gate_type
        self.use_spatial_attention = use_spatial_attention
        self.use_channel_attention = use_channel_attention
        
        # 特征投影层
        self.proj_skip = nn.Conv2d(cin_skip, cout, kernel_size=1, bias=False)
        self.proj_up = nn.Conv2d(cin_up, cout, kernel_size=1, bias=False)
        
        # 激活函数
        if activation == "relu":
            self.activation = nn.ReLU(inplace=True)
        elif activation == "gelu":
            self.activation = nn.GELU()
        elif activation == "swish":
            self.activation = nn.SiLU(inplace=True)
        else:
            self.activation = nn.ReLU(inplace=True)
        
        # 门控机制
        if gate_type == "attention":
            self.gate = nn.Sequential(
                nn.Conv2d(cout * 2, cout, kernel_size=1),
                self.activation,
                nn.Conv2d(cout, cout, kernel_size=3, padding=1),
                nn.BatchNorm2d(cout),
                self.activation,
                nn.Conv2d(cout, cout, kernel_size=1),
                nn.Sigmoid()
            )
        elif gate_type == "sigmoid":
            self.gate = nn.Sequential(
                nn.Conv2d(cout * 2, cout, kernel_size=1),
                self.activation,
                nn.Conv2d(cout, cout, kernel_size=1),
                nn.Sigmoid()
            )
        elif gate_type == "softmax":
            # 生成两个权重，和为1
            self.gate = nn.Sequential(
                nn.Conv2d(cout * 2, cout * 2, kernel_size=1),
                self.activation,
                nn.Conv2d(cout * 2, 2, kernel_size=1)  # 输出2个权重
            )
        elif gate_type == "se":  # Squeeze-and-Excitation
            self.gate = SEBlock(cout * 2, reduction=16)
        
        # 空间注意力模块 (可选)
        if self.use_spatial_attention:
            self.spatial_attention = SpatialAttention(kernel_size=7)
        
        # 通道注意力模块 (可选)
        if self.use_channel_attention:
            self.channel_attention = ChannelAttention(cout, reduction=16)
        
        # 特征精炼层
        self.refine = nn.Sequential(
            nn.Conv2d(cout, cout, kernel_size=3, padding=1),
            nn.BatchNorm2d(cout),
            self.activation,
            nn.Dropout2d(dropout) if dropout > 0 else nn.Identity(),
            nn.Conv2d(cout, cout, kernel_size=3, padding=1),
            nn.BatchNorm2d(cout)
        )
        
        # 残差连接
        self.use_residual = (cin_skip == cout or cin_up == cout)
        if self.use_residual and cin_skip != cout and cin_up != cout:
            self.residual_proj = nn.Conv2d(cin_skip, cout, kernel_size=1)
        
        self._init_weights()
    
    def _init_weights(self):
        """初始化权重"""
        for m in self.modules():
            if isinstance(m, nn.Conv2d):
                nn.init.kaiming_normal_(m.weight, mode='fan_out', nonlinearity='relu')
                if m.bias is not None:
                    nn.init.constant_(m.bias, 0)
            elif isinstance(m, nn.BatchNorm2d):
                nn.init.constant_(m.weight, 1)
                nn.init.constant_(m.bias, 0)
            elif isinstance(m, nn.Linear):
                nn.init.trunc_normal_(m.weight, std=0.02)
                if m.bias is not None:
                    nn.init.constant_(m.bias, 0)
    
    def forward(self, up_feat: torch.Tensor, skip_feat: torch.Tensor) -> torch.Tensor:
        """
        前向传播
        
        Args:
            up_feat: [B, cin_up, H, W] 上采样特征
            skip_feat: [B, cin_skip, H, W] 跳跃连接特征
            
        Returns:
            fused_feat: [B, cout, H, W] 融合后的特征
        """
        
        # 确保空间尺寸匹配
        if up_feat.shape[-2:] != skip_feat.shape[-2:]:
            up_feat = F.interpolate(
                up_feat, size=skip_feat.shape[-2:],
                mode='bilinear', align_corners=False
            )
        
        # 特征投影
        up_proj = self.proj_up(up_feat)        # [B, cout, H, W]
        skip_proj = self.proj_skip(skip_feat)  # [B, cout, H, W]
        
        # 门控融合
        if self.gate_type == "softmax":
            # 生成两个权重
            cat_feat = torch.cat([up_proj, skip_proj], dim=1)  # [B, cout*2, H, W]
            weights = self.gate(cat_feat)  # [B, 2, H, W]
            weights = F.softmax(weights, dim=1)
            
            w_up = weights[:, 0:1, :, :]     # [B, 1, H, W]
            w_skip = weights[:, 1:2, :, :]   # [B, 1, H, W]
            
            fused = w_up * up_proj + w_skip * skip_proj
        
        elif self.gate_type == "se":
            # SE门控
            cat_feat = torch.cat([up_proj, skip_proj], dim=1)  # [B, cout*2, H, W]
            se_weights = self.gate(cat_feat)  # [B, cout*2, 1, 1]
            
            # 分离权重
            w_up = se_weights[:, :self.cout, :, :]
            w_skip = se_weights[:, self.cout:, :, :]
            
            fused = w_up * up_proj + w_skip * skip_proj
        
        else:  # "attention", "sigmoid"
            # 标准门控
            cat_feat = torch.cat([up_proj, skip_proj], dim=1)  # [B, cout*2, H, W]
            gate = self.gate(cat_feat)  # [B, cout, H, W]
            
            fused = gate * up_proj + (1 - gate) * skip_proj
        
        # 空间注意力 (可选)
        if self.use_spatial_attention:
            spatial_attn = self.spatial_attention(fused)
            fused = fused * spatial_attn
        
        # 通道注意力 (可选)
        if self.use_channel_attention:
            channel_attn = self.channel_attention(fused)
            fused = fused * channel_attn
        
        # 特征精炼
        refined = self.refine(fused)
        
        # 残差连接
        if self.use_residual:
            if hasattr(self, 'residual_proj'):
                residual = self.residual_proj(skip_feat)
            elif skip_feat.size(1) == self.cout:
                residual = skip_feat
            elif up_feat.size(1) == self.cout:
                residual = up_feat
            else:
                residual = None
            
            if residual is not None and residual.shape[-2:] == refined.shape[-2:]:
                refined = refined + residual
        
        return self.activation(refined)


class SpatialAttention(nn.Module):
    """空间注意力模块"""
    
    def __init__(self, kernel_size: int = 7):
        super().__init__()
        self.conv = nn.Conv2d(
            2, 1, kernel_size=kernel_size, padding=kernel_size // 2, bias=False
        )
        self.sigmoid = nn.Sigmoid()
    
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        # 计算通道维度的平均值和最大值
        avg_out = torch.mean(x, dim=1, keepdim=True)  # [B, 1, H, W]
        max_out, _ = torch.max(x, dim=1, keepdim=True)  # [B, 1, H, W]
        
        # 拼接并卷积
        cat = torch.cat([avg_out, max_out], dim=1)  # [B, 2, H, W]
        attention = self.conv(cat)  # [B, 1, H, W]
        
        return self.sigmoid(attention)


class ChannelAttention(nn.Module):
    """通道注意力模块"""
    
    def __init__(self, in_channels: int, reduction: int = 16):
        super().__init__()
        self.avg_pool = nn.AdaptiveAvgPool2d(1)
        self.max_pool = nn.AdaptiveMaxPool2d(1)
        
        self.fc = nn.Sequential(
            nn.Linear(in_channels, in_channels // reduction, bias=False),
            nn.ReLU(inplace=True),
            nn.Linear(in_channels // reduction, in_channels, bias=False)
        )
        self.sigmoid = nn.Sigmoid()
    
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        B, C, H, W = x.shape
        
        # 平均池化和最大池化
        avg_out = self.avg_pool(x).view(B, C)  # [B, C]
        max_out = self.max_pool(x).view(B, C)  # [B, C]
        
        # 通过FC层
        avg_attn = self.fc(avg_out)  # [B, C]
        max_attn = self.fc(max_out)  # [B, C]
        
        # 合并注意力
        attention = self.sigmoid(avg_attn + max_attn)  # [B, C]
        
        return attention.view(B, C, 1, 1)


class SEBlock(nn.Module):
    """Squeeze-and-Excitation Block"""
    
    def __init__(self, in_channels: int, reduction: int = 16):
        super().__init__()
        self.squeeze = nn.AdaptiveAvgPool2d(1)
        self.excitation = nn.Sequential(
            nn.Linear(in_channels, in_channels // reduction, bias=False),
            nn.ReLU(inplace=True),
            nn.Linear(in_channels // reduction, in_channels, bias=False),
            nn.Sigmoid()
        )
    
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        B, C, H, W = x.shape
        
        # Squeeze
        squeeze = self.squeeze(x).view(B, C)  # [B, C]
        
        # Excitation
        excitation = self.excitation(squeeze)  # [B, C]
        
        return excitation.view(B, C, 1, 1)


class MultiScaleGatedFuse(nn.Module):
    """
    多尺度门控融合模块，用于同时处理多个尺度的特征融合
    """
    
    def __init__(
        self,
        scale_configs: List[Tuple[int, int, int]],  # [(cin_skip, cin_up, cout), ...]
        gate_type: str = "attention",
        **kwargs
    ):
        super().__init__()
        
        self.scales = nn.ModuleList([
            GatedFuse(cin_skip, cin_up, cout, gate_type=gate_type, **kwargs)
            for cin_skip, cin_up, cout in scale_configs
        ])
        
        # 跨尺度融合 (可选)
        self.use_cross_scale_fusion = kwargs.get('use_cross_scale_fusion', False)
        if self.use_cross_scale_fusion:
            total_cout = sum(cout for _, _, cout in scale_configs)
            self.cross_scale_fusion = nn.Sequential(
                nn.Conv2d(total_cout, total_cout // 2, kernel_size=1),
                nn.ReLU(inplace=True),
                nn.Conv2d(total_cout // 2, scale_configs[-1][2], kernel_size=1)
            )
    
    def forward(
        self,
        up_feats: List[torch.Tensor],
        skip_feats: List[torch.Tensor]
    ) -> List[torch.Tensor]:
        """
        多尺度融合
        
        Args:
            up_feats: 上采样特征列表
            skip_feats: 跳跃连接特征列表
            
        Returns:
            fused_feats: 融合后的特征列表
        """
        
        assert len(up_feats) == len(skip_feats) == len(self.scales)
        
        fused_feats = []
        for i, (up_feat, skip_feat, fuse_layer) in enumerate(
            zip(up_feats, skip_feats, self.scales)
        ):
            fused = fuse_layer(up_feat, skip_feat)
            fused_feats.append(fused)
        
        # 跨尺度融合 (可选)
        if self.use_cross_scale_fusion and len(fused_feats) > 1:
            # 将所有特征调整到相同尺寸
            target_size = fused_feats[-1].shape[-2:]
            aligned_feats = []
            
            for feat in fused_feats:
                if feat.shape[-2:] != target_size:
                    feat = F.interpolate(
                        feat, size=target_size,
                        mode='bilinear', align_corners=False
                    )
                aligned_feats.append(feat)
            
            # 拼接并融合
            cat_feat = torch.cat(aligned_feats, dim=1)
            cross_fused = self.cross_scale_fusion(cat_feat)
            
            # 替换最后一个特征
            fused_feats[-1] = cross_fused
        
        return fused_feats


def create_gated_fuse(cin_skip, cin_up, cout, gate_type="attention", **kwargs):
    """创建门控融合模块的便捷函数"""
    return GatedFuse(
        cin_skip=cin_skip,
        cin_up=cin_up,
        cout=cout,
        gate_type=gate_type,
        **kwargs
    )


def create_lightweight_gated_fuse(cin_skip, cin_up, cout, **kwargs):
    """创建轻量级门控融合模块"""
    return GatedFuse(
        cin_skip=cin_skip,
        cin_up=cin_up,
        cout=cout,
        gate_type="sigmoid",
        use_spatial_attention=False,
        use_channel_attention=False,
        dropout=0.0,
        **kwargs
    )


def create_enhanced_gated_fuse(cin_skip, cin_up, cout, **kwargs):
    """创建增强型门控融合模块"""
    return GatedFuse(
        cin_skip=cin_skip,
        cin_up=cin_up,
        cout=cout,
        gate_type="attention",
        use_spatial_attention=True,
        use_channel_attention=True,
        dropout=0.1,
        **kwargs
    )