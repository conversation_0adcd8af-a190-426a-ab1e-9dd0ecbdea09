"""
Enhanced Saliency Decoder: 增强型显著性解码器
集成门控融合、中心偏置先验和多尺度监督
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
import sys
import os

# 添加路径以支持导入
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(os.path.dirname(os.path.dirname(current_dir)))
sys.path.insert(0, project_root)

from .gated_fusion import GatedFuse, MultiScaleGatedFuse
from .center_bias import GaussianMixtureCenterBias


class EnhancedSaliencyDecoder(nn.Module):
    """
    增强型显著性解码器
    
    特性：
    1. 门控融合替换简单skip连接
    2. 可学习中心偏置先验
    3. 多尺度监督输出
    4. 注意力池化机制
    """
    
    def __init__(
        self,
        embed_dim: int = 768,
        output_size: tuple = (224, 384),
        hidden_dim: int = 256,
        use_gated_fusion: bool = True,
        use_center_bias: bool = True,
        use_multiscale_supervision: bool = True,
        use_attention_pooling: bool = True,
        gate_type: str = "se",  # "se", "cbam", "simple"
        center_bias_type: str = "learnable",  # "learnable", "fixed", "adaptive"
        num_center_gaussians: int = 3
    ):
        super().__init__()
        
        self.embed_dim = embed_dim
        self.output_size = output_size
        self.hidden_dim = hidden_dim
        self.use_gated_fusion = use_gated_fusion
        self.use_center_bias = use_center_bias
        self.use_multiscale_supervision = use_multiscale_supervision
        self.use_attention_pooling = use_attention_pooling
        
        # 特征聚合和投影
        self.feat_proj = nn.Sequential(
            nn.Linear(embed_dim, hidden_dim),
            nn.GELU(),
            nn.Dropout(0.1),
            nn.Linear(hidden_dim, hidden_dim),
            nn.LayerNorm(hidden_dim)
        )
        
        # 全局特征处理
        self.global_proj = nn.Sequential(
            nn.Linear(embed_dim, hidden_dim),
            nn.GELU(),
            nn.Dropout(0.1)
        )
        
        # 注意力池化（如果启用）
        if self.use_attention_pooling:
            self.attention_pool = nn.MultiheadAttention(
                embed_dim=hidden_dim,
                num_heads=8,
                dropout=0.1,
                batch_first=True
            )
            self.pool_query = nn.Parameter(torch.randn(1, 1, hidden_dim))
        
        # 上采样解码器架构
        self.decoder_channels = [hidden_dim, hidden_dim//2, hidden_dim//4, hidden_dim//8, hidden_dim//16]
        
        # 上采样层
        self.upsamples = nn.ModuleList()
        for i in range(len(self.decoder_channels) - 1):
            self.upsamples.append(
                nn.ConvTranspose2d(
                    self.decoder_channels[i], 
                    self.decoder_channels[i+1],
                    kernel_size=4, stride=2, padding=1, bias=False
                )
            )
        
        # 批量归一化和激活
        self.batch_norms = nn.ModuleList([
            nn.BatchNorm2d(ch) for ch in self.decoder_channels[1:]
        ])
        
        # 融合层
        if self.use_gated_fusion:
            # 门控融合单元
            self.fusions = nn.ModuleList()
            for i in range(len(self.decoder_channels) - 2):
                self.fusions.append(
                    GatedFuse(
                        cin_skip=self.decoder_channels[i],  # 来自更深层的特征
                        cin_up=self.decoder_channels[i+1],   # 上采样特征
                        cout=self.decoder_channels[i+1],
                        gate_type=gate_type
                    )
                )
        else:
            # 简单融合
            self.fusions = nn.ModuleList([
                nn.Conv2d(self.decoder_channels[i] + self.decoder_channels[i+1], 
                         self.decoder_channels[i+1], kernel_size=1)
                for i in range(len(self.decoder_channels) - 2)
            ])
        
        # 最终输出层
        self.final_conv = nn.Conv2d(self.decoder_channels[-1], 1, kernel_size=3, padding=1)
        
        # 多尺度监督输出（如果启用）
        if self.use_multiscale_supervision:
            self.multiscale_outputs = nn.ModuleList([
                nn.Conv2d(ch, 1, kernel_size=3, padding=1)
                for ch in self.decoder_channels[1:-1]  # 中间层的输出
            ])
        
        # 中心偏置先验（如果启用）
        if self.use_center_bias:
            if center_bias_type == "learnable":
                self.center_bias = GaussianMixtureCenterBias(
                    output_size=output_size,
                    num_gaussians=num_center_gaussians,
                    learnable=True
                )
            elif center_bias_type == "fixed":
                from .center_bias import FixedCenterBias
                self.center_bias = FixedCenterBias(
                    output_size=output_size,
                    bias_type="gaussian"
                )
            elif center_bias_type == "adaptive":
                from .center_bias import DatasetAdaptiveCenterBias
                self.center_bias = DatasetAdaptiveCenterBias(
                    output_size=output_size,
                    dataset_type="general"
                )
        
        self._init_weights()
    
    def _init_weights(self):
        """初始化权重"""
        for m in self.modules():
            if isinstance(m, nn.Linear):
                nn.init.trunc_normal_(m.weight, std=0.02)
                if m.bias is not None:
                    nn.init.constant_(m.bias, 0)
            elif isinstance(m, (nn.Conv2d, nn.ConvTranspose2d)):
                nn.init.kaiming_normal_(m.weight, mode='fan_out', nonlinearity='relu')
                if m.bias is not None:
                    nn.init.constant_(m.bias, 0)
            elif isinstance(m, (nn.BatchNorm2d, nn.LayerNorm)):
                nn.init.constant_(m.bias, 0)
                nn.init.constant_(m.weight, 1.0)
    
    def spatial_reshape(self, features, target_spatial_dim):
        """将1D特征重新整理为2D空间特征图"""
        B, N, D = features.shape
        
        # 尝试推断合适的空间维度
        grid_size = int(np.sqrt(N))
        
        if grid_size * grid_size == N:
            # 完美平方数，直接重整
            feat_map = features.transpose(1, 2).view(B, D, grid_size, grid_size)
        else:
            # 不是完美平方数，使用适应性重整
            # 计算最接近的矩形网格
            possible_h = int(np.sqrt(N))
            possible_w = N // possible_h
            
            if possible_h * possible_w == N:
                feat_map = features.transpose(1, 2).view(B, D, possible_h, possible_w)
            else:
                # 最后的备选：全局平均池化
                features_mean = features.mean(dim=1, keepdim=True)  # [B, 1, D]
                feat_map = features_mean.transpose(1, 2).view(B, D, 1, 1)
                feat_map = feat_map.repeat(1, 1, target_spatial_dim, target_spatial_dim)
        
        return feat_map
    
    def forward(self, patch_feats, global_feat):
        """
        前向传播
        
        Args:
            patch_feats: [B, N, D] - patch特征
            global_feat: [B, D] - 全局特征（CLS token）
        
        Returns:
            outputs: dict - 包含主要输出和多尺度输出的字典
        """
        B, N, D = patch_feats.shape
        outputs = {}
        
        # 投影特征
        patch_feats = self.feat_proj(patch_feats)  # [B, N, hidden_dim]
        global_feat = self.global_proj(global_feat)  # [B, hidden_dim]
        
        # 融合全局和局部特征
        if self.use_attention_pooling:
            # 使用注意力机制融合
            pool_query = self.pool_query.expand(B, -1, -1)  # [B, 1, hidden_dim]
            
            # 全局特征作为query，patch特征作为key和value
            global_enhanced, _ = self.attention_pool(
                pool_query, patch_feats, patch_feats
            )
            global_enhanced = global_enhanced.squeeze(1)  # [B, hidden_dim]
            
            # 结合原始全局特征
            global_feat = global_feat + global_enhanced
        
        # 将全局特征广播到所有patch
        global_feat_expanded = global_feat.unsqueeze(1).expand(-1, N, -1)  # [B, N, hidden_dim]
        fused_feats = patch_feats + global_feat_expanded
        
        # 重新整理为空间特征图
        initial_size = 14  # 假设初始特征图大小为14x14
        x = self.spatial_reshape(fused_feats, initial_size)  # [B, hidden_dim, 14, 14]
        
        # 存储中间特征用于跳跃连接
        skip_features = [x]
        
        # 逐层上采样解码
        for i, (upsample, bn) in enumerate(zip(self.upsamples, self.batch_norms)):
            # 上采样
            x = upsample(x)  # [B, hidden_dim//(2^(i+1)), H*(2^(i+1)), W*(2^(i+1))]
            x = bn(x)
            x = F.relu(x, inplace=True)
            
            # 门控融合（除了最后一层）
            if i < len(self.fusions):
                skip_feat = skip_features[-1]  # 最近的skip特征
                
                # 调整skip特征尺寸匹配当前特征
                if skip_feat.shape[-2:] != x.shape[-2:]:
                    skip_feat = F.interpolate(
                        skip_feat, size=x.shape[-2:],
                        mode='bilinear', align_corners=False
                    )
                
                if self.use_gated_fusion:
                    # 使用门控融合
                    x = self.fusions[i](x, skip_feat)
                else:
                    # 简单融合
                    fused = torch.cat([x, skip_feat], dim=1)
                    x = self.fusions[i](fused)
                    x = F.relu(x, inplace=True)
            
            # 多尺度监督输出
            if self.use_multiscale_supervision and i < len(self.multiscale_outputs):
                scale_output = self.multiscale_outputs[i](x)
                scale_output = torch.sigmoid(scale_output)
                
                # 调整到目标尺寸
                if scale_output.shape[-2:] != self.output_size:
                    scale_output = F.interpolate(
                        scale_output, size=self.output_size,
                        mode='bilinear', align_corners=False
                    )
                
                outputs[f'scale_{i+1}'] = scale_output
            
            # 更新skip features
            skip_features.append(x)
        
        # 最终输出
        saliency_map = self.final_conv(x)
        saliency_map = torch.sigmoid(saliency_map)
        
        # 调整到目标尺寸
        if saliency_map.shape[-2:] != self.output_size:
            saliency_map = F.interpolate(
                saliency_map, size=self.output_size,
                mode='bilinear', align_corners=False
            )
        
        # 添加中心偏置先验
        if self.use_center_bias:
            center_bias_map = self.center_bias(batch_size=B)  # [B, 1, H, W]
            
            # 确保尺寸匹配
            if center_bias_map.shape[-2:] != saliency_map.shape[-2:]:
                center_bias_map = F.interpolate(
                    center_bias_map, size=saliency_map.shape[-2:],
                    mode='bilinear', align_corners=False
                )
            
            # 加性融合中心偏置
            saliency_map = saliency_map + 0.1 * center_bias_map  # 可调节权重
            saliency_map = torch.clamp(saliency_map, 0, 1)  # 确保在[0,1]范围内
        
        outputs['saliency_map'] = saliency_map
        
        return outputs
    
    def get_center_bias_parameters(self):
        """获取中心偏置参数（用于分析）"""
        if self.use_center_bias and hasattr(self.center_bias, 'get_parameters_dict'):
            return self.center_bias.get_parameters_dict()
        else:
            return None
    
    def visualize_center_bias(self, save_path=None):
        """可视化中心偏置"""
        if self.use_center_bias and hasattr(self.center_bias, 'visualize_bias'):
            return self.center_bias.visualize_bias(save_path)
        else:
            print("Center bias not available or doesn't support visualization")
            return None


def create_enhanced_saliency_decoder(embed_dim=768, output_size=(224, 384), **kwargs):
    """创建增强型显著性解码器的便捷函数"""
    return EnhancedSaliencyDecoder(
        embed_dim=embed_dim,
        output_size=output_size,
        **kwargs
    )


# 预定义配置
def create_lightweight_enhanced_decoder(**kwargs):
    """轻量级增强解码器"""
    return create_enhanced_saliency_decoder(
        embed_dim=384,
        hidden_dim=128,
        use_gated_fusion=True,
        use_center_bias=False,
        use_multiscale_supervision=False,
        gate_type="simple",
        **kwargs
    )


def create_standard_enhanced_decoder(**kwargs):
    """标准增强解码器"""
    return create_enhanced_saliency_decoder(
        embed_dim=768,
        hidden_dim=256,
        use_gated_fusion=True,
        use_center_bias=True,
        use_multiscale_supervision=True,
        gate_type="se",
        center_bias_type="learnable",
        **kwargs
    )


def create_premium_enhanced_decoder(**kwargs):
    """高级增强解码器"""
    return create_enhanced_saliency_decoder(
        embed_dim=1024,
        hidden_dim=512,
        use_gated_fusion=True,
        use_center_bias=True,
        use_multiscale_supervision=True,
        gate_type="cbam",
        center_bias_type="adaptive",
        num_center_gaussians=5,
        **kwargs
    )