"""
视觉分支动态Tokenizer

基于DART的视觉tokenizer，支持：
- 图像/视频帧的自适应分块
- 内容感知的patch采样
- 可变token数量预算
- 音频能量驱动的token分配调整
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Optional, Tuple, Union, Dict
import sys
import os

# 添加相对路径
sys.path.append(os.path.dirname(__file__))
from ..adapters.dart_adapter import VisualDARTAdapter


class VisualDARTTokenizer(nn.Module):
    """
    视觉DART Tokenizer
    
    核心功能：
    1. 接收图像/视频帧 [B, C, H, W]
    2. 基于内容重要性自适应分块
    3. 支持动态token数量（由预算控制器分配）
    4. 可选的音频能量门控调节
    
    Args:
        input_size: 输入图像尺寸，默认224
        patch_size: patch大小，默认16  
        in_chans: 输入通道数，默认3（RGB）
        embed_dim: 输出嵌入维度，默认768
        scorer_type: 评分网络类型
        enable_audio_gating: 是否启用音频门控
        audio_gate_ratio: 音频门控的影响比例
    """
    
    def __init__(
        self,
        input_size: Union[int, Tuple[int, int]] = 224,
        patch_size: int = 16,
        in_chans: int = 3,
        embed_dim: int = 768,
        scorer_type: str = "mobilenet_small",
        enable_audio_gating: bool = False,
        audio_gate_ratio: float = 0.2,
        **kwargs
    ):
        super().__init__()
        
        self.input_size = input_size if isinstance(input_size, tuple) else (input_size, input_size)
        self.patch_size = patch_size
        self.in_chans = in_chans  
        self.embed_dim = embed_dim
        self.enable_audio_gating = enable_audio_gating
        self.audio_gate_ratio = audio_gate_ratio
        
        # DART适配器
        self.dart_adapter = VisualDARTAdapter(
            input_size=input_size,
            patch_size=patch_size,
            in_chans=in_chans,
            embed_dim=embed_dim,
            scorer_type=scorer_type,
            **kwargs
        )
        
        # 音频门控模块（可选）
        if enable_audio_gating:
            self.audio_gate = AudioGate(embed_dim)
        
        # 位置编码
        self.pos_encoding_type = "learned"  # "learned" or "sinusoidal"
        if self.pos_encoding_type == "learned":
            max_patches = self.dart_adapter.num_patches_default * 2  # 预留空间
            self.pos_embed = nn.Parameter(torch.zeros(1, max_patches, embed_dim))
            nn.init.trunc_normal_(self.pos_embed, std=0.02)
    
    def forward(
        self,
        frames: torch.Tensor,
        num_patches: int,
        audio_energy: Optional[torch.Tensor] = None,
        return_dict: bool = False
    ) -> Union[torch.Tensor, Dict]:
        """
        前向传播
        
        Args:
            frames: 输入帧 [B, C, H, W] 或 [B, T, C, H, W]
            num_patches: 分配的视觉token数量 (N_v)
            audio_energy: 音频能量 [B, T] 用于门控调节（可选）
            return_dict: 是否返回详细信息
            
        Returns:
            visual_tokens: 视觉token [B, N_v, D]
            pos_encoding: 位置编码 [B, N_v, D]
            或包含详细信息的字典
        """
        # 处理输入维度 - 自动检测和适配
        if frames.dim() == 5:  # 视频输入 [B, T, C, H, W]
            B, T, C, H, W = frames.shape
            # 重塑为batch处理: [B*T, C, H, W]
            frames = frames.view(B * T, C, H, W)
            is_video = True
        elif frames.dim() == 4:  # 图像输入 [B, C, H, W]
            B, C, H, W = frames.shape
            T = 1
            is_video = False
        else:
            raise ValueError(f"Unsupported input dimensions: {frames.shape}. Expected 4D [B,C,H,W] or 5D [B,T,C,H,W]")
        
        # 动态tokenization
        result = self.dart_adapter(
            frames,
            num_patches=num_patches,
            return_dict=True  # 总是获取详细信息
        )
        
        visual_tokens = result['x']  # [B*T, N_v, D]
        
        # 位置编码
        if self.pos_encoding_type == "learned":
            pos_encoding = self.pos_embed[:, :num_patches, :].expand(visual_tokens.size(0), -1, -1)
        else:
            # 使用DART提供的位置信息
            if 'pos' in result:
                pos_encoding = result['pos']
            else:
                pos_encoding = self.dart_adapter.get_pos_encoding(visual_tokens, result.get('new_edges', None))
        
        # 音频门控调节（可选）
        if self.enable_audio_gating and audio_energy is not None:
            visual_tokens = self.apply_audio_gating(visual_tokens, audio_energy, B, T)
        
        # 恢复视频维度
        if is_video:
            visual_tokens = visual_tokens.view(B, T, num_patches, self.embed_dim)
            pos_encoding = pos_encoding.view(B, T, num_patches, self.embed_dim)
        
        if return_dict:
            output = {
                'visual_tokens': visual_tokens,
                'pos_encoding': pos_encoding,
                'num_patches': num_patches,
                'importance_map': result.get('score', None),
                'patch_positions': result.get('new_edges', None),
                'input_shape': (B, T, C, H, W) if is_video else (B, C, H, W)
            }
            return output
        else:
            return visual_tokens, pos_encoding
    
    def apply_audio_gating(
        self,
        visual_tokens: torch.Tensor,
        audio_energy: torch.Tensor,
        B: int,
        T: int
    ) -> torch.Tensor:
        """
        应用音频门控调节视觉tokens
        
        Args:
            visual_tokens: 视觉token [B*T, N_v, D]
            audio_energy: 音频能量 - 容错处理不同形状
            B, T: 批次大小和时间维度
            
        Returns:
            调节后的视觉tokens
        """

        
        # 容错处理音频能量的不同维度
        if audio_energy.dim() == 1:  # [B*T] 或 [N]
            if audio_energy.size(0) == B * T:
                # 已经是正确的形状
                audio_energy_reshaped = audio_energy.view(B * T, 1, 1)
            elif audio_energy.size(0) == B:
                # [B] -> [B*T, 1, 1]
                audio_energy_reshaped = audio_energy.unsqueeze(1).repeat(1, T).view(B * T, 1, 1)
            else:
                # 不匹配，使用平均值
                mean_energy = audio_energy.mean()
                audio_energy_reshaped = mean_energy.expand(B * T, 1, 1)
                
        elif audio_energy.dim() == 2:  # [B, T] 或 [B, N]
            if audio_energy.shape == (B, T):
                # [B, T] -> [B*T, 1, 1]
                audio_energy_reshaped = audio_energy.view(B * T, 1, 1)
            else:
                # 其他[B, N]形状，取平均
                audio_energy_flat = audio_energy.mean(dim=1)  # [B]
                audio_energy_reshaped = audio_energy_flat.unsqueeze(1).repeat(1, T).view(B * T, 1, 1)
                
        else:
            # 更高维度，展平并取平均
            audio_energy_flat = audio_energy.view(B, -1).mean(dim=1)  # [B]
            audio_energy_reshaped = audio_energy_flat.unsqueeze(1).repeat(1, T).view(B * T, 1, 1)
        

        
        # 计算门控权重（归一化到合理范围）
        gate_weight = torch.sigmoid(audio_energy_reshaped * 2.0)  # [B*T, 1, 1]
        
        # 线性插值：高音频能量 -> 更强的视觉表示
        baseline_weight = 1.0 - self.audio_gate_ratio
        adaptive_weight = baseline_weight + self.audio_gate_ratio * gate_weight
        
        return visual_tokens * adaptive_weight
    
    def get_num_patches_default(self) -> int:
        """获取默认patch数量"""
        return self.dart_adapter.num_patches_default
    
    def get_grid_size(self) -> Tuple[int, int]:
        """获取网格尺寸"""
        return self.dart_adapter.grid_size


class AudioGate(nn.Module):
    """
    音频门控模块
    
    使用音频能量信息调节视觉token的表示强度
    """
    
    def __init__(self, embed_dim: int):
        super().__init__()
        self.embed_dim = embed_dim
        
        # 简单的门控网络
        self.gate_net = nn.Sequential(
            nn.Linear(1, embed_dim // 4),
            nn.ReLU(),
            nn.Linear(embed_dim // 4, embed_dim),
            nn.Sigmoid()
        )
    
    def forward(self, visual_tokens: torch.Tensor, audio_energy: torch.Tensor) -> torch.Tensor:
        """
        Args:
            visual_tokens: [B, N, D]
            audio_energy: [B, 1] 
            
        Returns:
            gated_tokens: [B, N, D]
        """
        B, N, D = visual_tokens.shape
        
        # 生成门控权重
        gate_weights = self.gate_net(audio_energy.unsqueeze(-1))  # [B, D]
        gate_weights = gate_weights.unsqueeze(1).expand(-1, N, -1)  # [B, N, D]
        
        return visual_tokens * gate_weights


class TemporalVisualTokenizer(VisualDARTTokenizer):
    """
    时序视觉Tokenizer
    
    专门处理视频序列，包含时序建模能力
    支持跨帧的动态token分配
    """
    
    def __init__(
        self,
        input_size: Union[int, Tuple[int, int]] = 224,
        patch_size: int = 16,
        in_chans: int = 3,
        embed_dim: int = 768,
        max_temporal_len: int = 16,
        temporal_fusion: str = "attention",  # "attention", "conv1d", "none"
        **kwargs
    ):
        super().__init__(
            input_size=input_size,
            patch_size=patch_size,
            in_chans=in_chans,
            embed_dim=embed_dim,
            **kwargs
        )
        
        self.max_temporal_len = max_temporal_len
        self.temporal_fusion = temporal_fusion
        
        # 时序融合模块
        if temporal_fusion == "attention":
            self.temporal_attn = nn.MultiheadAttention(embed_dim, num_heads=8, batch_first=True)
        elif temporal_fusion == "conv1d":
            self.temporal_conv = nn.Conv1d(embed_dim, embed_dim, kernel_size=3, padding=1)
        
        # 时序位置编码
        self.temporal_pos_embed = nn.Parameter(torch.zeros(1, max_temporal_len, embed_dim))
        nn.init.trunc_normal_(self.temporal_pos_embed, std=0.02)
    
    def forward(
        self,
        frames: torch.Tensor,
        num_patches: int,
        audio_energy: Optional[torch.Tensor] = None,
        return_dict: bool = False
    ) -> Union[torch.Tensor, Dict]:
        """
        处理视频序列
        
        Args:
            frames: 视频帧 [B, T, C, H, W]
            num_patches: 每帧的patch数量
            audio_energy: 音频能量 [B, T]
            return_dict: 是否返回详细信息
            
        Returns:
            时序融合后的视觉tokens
        """
        assert frames.dim() == 5, "TemporalVisualTokenizer requires 5D input [B,T,C,H,W]"
        
        B, T, C, H, W = frames.shape
        
        # 按帧处理
        frame_tokens = []
        frame_pos = []
        
        for t in range(T):
            frame = frames[:, t]  # [B, C, H, W]
            
            # 单帧tokenization
            tokens, pos = super().forward(
                frame,
                num_patches=num_patches,
                audio_energy=audio_energy[:, t:t+1] if audio_energy is not None else None,
                return_dict=False
            )
            
            frame_tokens.append(tokens)  # [B, N, D]
            frame_pos.append(pos)
        
        # 堆叠所有帧
        visual_tokens = torch.stack(frame_tokens, dim=1)  # [B, T, N, D]
        pos_encoding = torch.stack(frame_pos, dim=1)  # [B, T, N, D]
        
        # 时序融合
        if self.temporal_fusion == "attention":
            visual_tokens = self.apply_temporal_attention(visual_tokens)
        elif self.temporal_fusion == "conv1d":
            visual_tokens = self.apply_temporal_conv(visual_tokens)
        
        if return_dict:
            return {
                'visual_tokens': visual_tokens,
                'pos_encoding': pos_encoding,
                'num_patches': num_patches,
                'temporal_length': T
            }
        else:
            return visual_tokens, pos_encoding
    
    def apply_temporal_attention(self, visual_tokens: torch.Tensor) -> torch.Tensor:
        """
        应用时序注意力融合
        
        Args:
            visual_tokens: [B, T, N, D]
            
        Returns:
            fused_tokens: [B, T, N, D]
        """
        B, T, N, D = visual_tokens.shape
        
        # 重新排列为 [B*N, T, D] 以便对每个空间位置应用时序注意力
        tokens_reshape = visual_tokens.permute(0, 2, 1, 3).reshape(B * N, T, D)
        
        # 添加时序位置编码
        temporal_pos = self.temporal_pos_embed[:, :T, :].expand(B * N, -1, -1)
        tokens_reshape = tokens_reshape + temporal_pos
        
        # 自注意力
        fused_tokens, _ = self.temporal_attn(tokens_reshape, tokens_reshape, tokens_reshape)
        
        # 恢复形状
        fused_tokens = fused_tokens.reshape(B, N, T, D).permute(0, 2, 1, 3)  # [B, T, N, D]
        
        return fused_tokens
    
    def apply_temporal_conv(self, visual_tokens: torch.Tensor) -> torch.Tensor:
        """
        应用1D卷积进行时序融合
        
        Args:
            visual_tokens: [B, T, N, D]
            
        Returns:
            fused_tokens: [B, T, N, D]
        """
        B, T, N, D = visual_tokens.shape
        
        # 重新排列为 [B*N, D, T] 以便应用1D卷积
        tokens_reshape = visual_tokens.permute(0, 2, 3, 1).reshape(B * N, D, T)
        
        # 1D卷积
        fused_tokens = self.temporal_conv(tokens_reshape)
        
        # 恢复形状
        fused_tokens = fused_tokens.reshape(B, N, D, T).permute(0, 3, 1, 2)  # [B, T, N, D]
        
        return fused_tokens


# 便利函数
def create_visual_tokenizer(
    input_size: Union[int, Tuple[int, int]] = 224,
    embed_dim: int = 768,
    enable_audio_gating: bool = False,
    temporal_mode: bool = False,
    **kwargs
) -> Union[VisualDARTTokenizer, TemporalVisualTokenizer]:
    """
    创建视觉tokenizer的便利函数
    
    Args:
        input_size: 输入尺寸
        embed_dim: 嵌入维度
        enable_audio_gating: 是否启用音频门控
        temporal_mode: 是否使用时序模式
        **kwargs: 其他参数
        
    Returns:
        视觉tokenizer实例
    """
    if temporal_mode:
        return TemporalVisualTokenizer(
            input_size=input_size,
            embed_dim=embed_dim,
            enable_audio_gating=enable_audio_gating,
            **kwargs
        )
    else:
        return VisualDARTTokenizer(
            input_size=input_size,
            embed_dim=embed_dim,
            enable_audio_gating=enable_audio_gating,
            **kwargs
        )