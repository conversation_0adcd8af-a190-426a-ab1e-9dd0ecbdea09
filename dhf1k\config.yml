data:
  channels: 1
  gaussian_dequantization: true
  image_size: 224
  logit_transform: false
  rescaled: false
  uniform_dequantization: false
  width: 384
diffusion:
  beta_end: 0.02
  beta_schedule: cosine
  beta_start: 0.0001
  num_diffusion_timesteps: 1000
loss:
  cc_weight: -0.1
  ce_weight: 1.0
  kl_weight: 1.0
  loss_cc: false
  loss_ce: false
  loss_kl: false
  loss_mse: true
  loss_nss: false
  loss_sim: false
  mse_weight: 1.0
  nss_weight: -0.1
  sim_weight: -0.1
model:
  attn_resolutions: []
  ch: 128
  ch_mult:
  - 1
  - 2
  - 2
  - 2
  - 4
  dropout: 0.1
  ema: false
  ema_rate: 0.9999
  in_channels: 1
  num_res_blocks: 2
  out_ch: 1
  resamp_with_conv: true
  type: simple
  var_type: fixedlarge
optim:
  amsgrad: false
  beta1: 0.9
  eps: 1.0e-08
  grad_clip: 1.0
  lr: 0.0001
  optimizer: Adam
  weight_decay: 0.0
sampling:
  batch_size: 2
  denoise: true
  dpm_solver_atol: 0.0078
  dpm_solver_method: multistep
  dpm_solver_order: 2
  dpm_solver_rtol: 0.05
  dpm_solver_type: dpmsolver
  eta: 0.0
  last_only: true
  lower_order_final: false
  sample_type: ddim
  skip_type: logSNR
  thresholding: false
  timesteps: 1
training:
  batch_size: 48
  log_freq: 200
  n_epochs: 3
  n_epochs_for_av_data: 4
  snapshot_freq: 5000
  training_target: x0
  validation_freq: 10000
