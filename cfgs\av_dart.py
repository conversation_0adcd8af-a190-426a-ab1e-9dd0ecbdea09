"""
AV-DART模型配置文件
基于audio_visual.py，但使用AV-DART模型替换原有的VideoSaliencyModel
"""

encoder_channel_list = [768, 384, 192, 96]  # for swin
decoder_channel_list = encoder_channel_list

import yaml
from util.opts import dict2namespace
from DART.av_dart.models.av_dart_diffusion_wrapper import AVDARTDiffusionWrapper
from datasets.dhf1k_data import DHF1KDatasetMultiFrames
from datasets.holly2wood_dataset import HollyDataset
from datasets.ucf_dataset import UCFDataset

with open("cfgs/diffusion.yml", "r") as f:
    diff_config = yaml.safe_load(f)

new_config = dict2namespace(diff_config)

len_snippet = 32
data_type = "dhf1k"
gt_length = 1  # 8, 10, 12, 14, 16
img_size = (224, 384)

# AV-DART模型配置
config = dict(
    type=AVDARTDiffusionWrapper,
    embed_dim=768,
    total_tokens=196,
    num_layers=6,
    num_heads=8,
    visual_size=224,
    audio_size=(64, 224),
    output_size=img_size,
    token_strategy="energy_based",  # 支持 "fixed", "dynamic", "energy_based"
    # 策略特定参数
    visual_ratio=0.6,
    min_visual_ratio=0.3,
    max_visual_ratio=0.8,
    energy_weight=0.4
)

data_dict = {
    "dhf1k": {
        "type": DHF1KDatasetMultiFrames,
        "path": "VideoSalPrediction/DHF1k_extracted",
    },
    "holly": {
        "type": HollyDataset,
        "path": "VideoSalPrediction/Hollywood2",
    },
    "ucf": {
        "type": UCFDataset,
        "path": "VideoSalPrediction/ucf",
    },
}

data = dict(
    train=dict(
        type=data_dict[data_type]["type"],
        path_data=data_dict[data_type]["path"],
        len_snippet=len_snippet,
        mode="train",
        img_size=img_size,
        alternate=1,
        gt_length=gt_length,
    ),
    val=dict(
        type=data_dict[data_type]["type"],
        path_data=data_dict[data_type]["path"],
        len_snippet=len_snippet,
        mode="val",
        img_size=img_size,
        alternate=1,
        gt_length=gt_length,
    ),
)