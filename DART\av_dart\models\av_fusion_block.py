"""
增强型音视频融合模块
实现多层交替式Self-Attention和Cross-Attention融合
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import math


class AVFusionBlock(nn.Module):
    """
    音视频融合块 - 交替式Self-Attention和Cross-Attention
    """
    
    def __init__(
        self,
        dim: int = 768,
        num_heads: int = 8,
        dropout: float = 0.1,
        mlp_ratio: float = 4.0,
        use_layer_scale: bool = False,
        layer_scale_init: float = 1e-6
    ):
        super().__init__()
        
        self.dim = dim
        self.num_heads = num_heads
        self.use_layer_scale = use_layer_scale
        
        # Visual Self-Attention
        self.visual_self_attn = nn.MultiheadAttention(
            embed_dim=dim,
            num_heads=num_heads,
            dropout=dropout,
            batch_first=True
        )
        self.visual_norm1 = nn.LayerNorm(dim)
        
        # Audio Self-Attention
        self.audio_self_attn = nn.MultiheadAttention(
            embed_dim=dim,
            num_heads=num_heads,
            dropout=dropout,
            batch_first=True
        )
        self.audio_norm1 = nn.LayerNorm(dim)
        
        # Cross-Attention: Visual <- Audio
        self.visual_cross_attn = nn.MultiheadAttention(
            embed_dim=dim,
            num_heads=num_heads,
            dropout=dropout,
            batch_first=True
        )
        self.visual_norm2 = nn.LayerNorm(dim)
        
        # Cross-Attention: Audio <- Visual
        self.audio_cross_attn = nn.MultiheadAttention(
            embed_dim=dim,
            num_heads=num_heads,
            dropout=dropout,
            batch_first=True
        )
        self.audio_norm2 = nn.LayerNorm(dim)
        
        # FFN for visual
        self.visual_mlp = nn.Sequential(
            nn.Linear(dim, int(dim * mlp_ratio)),
            nn.GELU(),
            nn.Dropout(dropout),
            nn.Linear(int(dim * mlp_ratio), dim),
            nn.Dropout(dropout)
        )
        self.visual_norm3 = nn.LayerNorm(dim)
        
        # FFN for audio
        self.audio_mlp = nn.Sequential(
            nn.Linear(dim, int(dim * mlp_ratio)),
            nn.GELU(),
            nn.Dropout(dropout),
            nn.Linear(int(dim * mlp_ratio), dim),
            nn.Dropout(dropout)
        )
        self.audio_norm3 = nn.LayerNorm(dim)
        
        # Layer Scale (可选)
        if use_layer_scale:
            self.visual_layer_scales = nn.ParameterList([
                nn.Parameter(layer_scale_init * torch.ones(dim)) for _ in range(3)
            ])
            self.audio_layer_scales = nn.ParameterList([
                nn.Parameter(layer_scale_init * torch.ones(dim)) for _ in range(3)
            ])
    
    def forward(self, visual_tokens, audio_tokens):
        """
        前向传播
        Args:
            visual_tokens: [B, Nv, D]
            audio_tokens: [B, Na, D]
        Returns:
            enhanced_visual: [B, Nv, D]
            enhanced_audio: [B, Na, D]
        """
        
        # === Stage 1: Self-Attention ===
        # Visual Self-Attention
        visual_self_out, _ = self.visual_self_attn(visual_tokens, visual_tokens, visual_tokens)
        if self.use_layer_scale:
            visual_tokens = visual_tokens + self.visual_layer_scales[0] * visual_self_out
        else:
            visual_tokens = visual_tokens + visual_self_out
        visual_tokens = self.visual_norm1(visual_tokens)
        
        # Audio Self-Attention
        audio_self_out, _ = self.audio_self_attn(audio_tokens, audio_tokens, audio_tokens)
        if self.use_layer_scale:
            audio_tokens = audio_tokens + self.audio_layer_scales[0] * audio_self_out
        else:
            audio_tokens = audio_tokens + audio_self_out
        audio_tokens = self.audio_norm1(audio_tokens)
        
        # === Stage 2: Cross-Attention ===
        # Visual <- Audio (audio作为key/value，visual作为query)
        visual_cross_out, _ = self.visual_cross_attn(visual_tokens, audio_tokens, audio_tokens)
        if self.use_layer_scale:
            visual_tokens = visual_tokens + self.visual_layer_scales[1] * visual_cross_out
        else:
            visual_tokens = visual_tokens + visual_cross_out
        visual_tokens = self.visual_norm2(visual_tokens)
        
        # Audio <- Visual (visual作为key/value，audio作为query)
        audio_cross_out, _ = self.audio_cross_attn(audio_tokens, visual_tokens, visual_tokens)
        if self.use_layer_scale:
            audio_tokens = audio_tokens + self.audio_layer_scales[1] * audio_cross_out
        else:
            audio_tokens = audio_tokens + audio_cross_out
        audio_tokens = self.audio_norm2(audio_tokens)
        
        # === Stage 3: FFN ===
        # Visual FFN
        visual_mlp_out = self.visual_mlp(visual_tokens)
        if self.use_layer_scale:
            visual_tokens = visual_tokens + self.visual_layer_scales[2] * visual_mlp_out
        else:
            visual_tokens = visual_tokens + visual_mlp_out
        visual_tokens = self.visual_norm3(visual_tokens)
        
        # Audio FFN
        audio_mlp_out = self.audio_mlp(audio_tokens)
        if self.use_layer_scale:
            audio_tokens = audio_tokens + self.audio_layer_scales[2] * audio_mlp_out
        else:
            audio_tokens = audio_tokens + audio_mlp_out
        audio_tokens = self.audio_norm3(audio_tokens)
        
        return visual_tokens, audio_tokens


class GatedFusion(nn.Module):
    """
    门控融合模块 - 支持多种门控机制
    """
    
    def __init__(
        self,
        embed_dim: int = 768,
        gate_type: str = "se",  # se, cbam, simple
        num_fusion_layers: int = 3,
        use_layer_scale: bool = False
    ):
        super().__init__()
        
        self.embed_dim = embed_dim
        self.gate_type = gate_type
        self.num_fusion_layers = num_fusion_layers
        
        # 多层AVFusion blocks
        self.fusion_blocks = nn.ModuleList([
            AVFusionBlock(
                dim=embed_dim,
                num_heads=8,
                dropout=0.1,
                use_layer_scale=use_layer_scale
            )
            for _ in range(num_fusion_layers)
        ])
        
        # 门控机制
        if gate_type == "se":
            self.gate = SEGate(embed_dim)
        elif gate_type == "cbam":
            self.gate = CBAMGate(embed_dim)
        elif gate_type == "simple":
            self.gate = SimpleGate(embed_dim)
        else:
            raise ValueError(f"不支持的门控类型: {gate_type}")
    
    def forward(self, visual_tokens, audio_tokens):
        """
        前向传播
        Args:
            visual_tokens: [B, Nv, D]
            audio_tokens: [B, Na, D]
        Returns:
            fused_visual: [B, Nv, D]
            fused_audio: [B, Na, D]
        """
        
        # 多层融合
        for fusion_block in self.fusion_blocks:
            visual_tokens, audio_tokens = fusion_block(visual_tokens, audio_tokens)
        
        # 门控机制
        gated_visual = self.gate(visual_tokens)
        gated_audio = self.gate(audio_tokens)
        
        return gated_visual, gated_audio


class SEGate(nn.Module):
    """Squeeze-and-Excitation门控"""
    
    def __init__(self, embed_dim, reduction=16):
        super().__init__()
        self.fc = nn.Sequential(
            nn.Linear(embed_dim, embed_dim // reduction),
            nn.ReLU(inplace=True),
            nn.Linear(embed_dim // reduction, embed_dim),
            nn.Sigmoid()
        )
    
    def forward(self, x):
        # x: [B, N, D]
        # Global average pooling
        se_weight = x.mean(dim=1)  # [B, D]
        se_weight = self.fc(se_weight).unsqueeze(1)  # [B, 1, D]
        return x * se_weight


class CBAMGate(nn.Module):
    """CBAM门控 (简化版)"""
    
    def __init__(self, embed_dim):
        super().__init__()
        # Channel attention
        self.channel_gate = nn.Sequential(
            nn.Linear(embed_dim, embed_dim // 16),
            nn.ReLU(),
            nn.Linear(embed_dim // 16, embed_dim),
            nn.Sigmoid()
        )
        
        # Spatial attention (对于token序列，相当于token attention)
        self.spatial_gate = nn.Sequential(
            nn.Conv1d(2, 1, kernel_size=7, padding=3),
            nn.Sigmoid()
        )
    
    def forward(self, x):
        # x: [B, N, D]
        B, N, D = x.shape
        
        # Channel attention
        channel_att = x.mean(dim=1)  # [B, D]
        channel_att = self.channel_gate(channel_att).unsqueeze(1)  # [B, 1, D]
        x = x * channel_att
        
        # Spatial attention
        max_out = torch.max(x, dim=2, keepdim=True)[0]  # [B, N, 1]
        avg_out = torch.mean(x, dim=2, keepdim=True)    # [B, N, 1]
        spatial_att = torch.cat([max_out, avg_out], dim=2)  # [B, N, 2]
        spatial_att = spatial_att.transpose(1, 2)  # [B, 2, N]
        spatial_att = self.spatial_gate(spatial_att).transpose(1, 2)  # [B, N, 1]
        
        return x * spatial_att


class SimpleGate(nn.Module):
    """简单门控"""
    
    def __init__(self, embed_dim):
        super().__init__()
        self.gate = nn.Sequential(
            nn.Linear(embed_dim, embed_dim),
            nn.Sigmoid()
        )
    
    def forward(self, x):
        return x * self.gate(x)


def create_av_fusion_block(dim=768, num_heads=8, **kwargs):
    """创建AV融合块的便捷函数"""
    return AVFusionBlock(dim=dim, num_heads=num_heads, **kwargs)


def create_gated_fusion(embed_dim=768, gate_type="se", **kwargs):
    """创建门控融合的便捷函数"""
    return GatedFusion(embed_dim=embed_dim, gate_type=gate_type, **kwargs)