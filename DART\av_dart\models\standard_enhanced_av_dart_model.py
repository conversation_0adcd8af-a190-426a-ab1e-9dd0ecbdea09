"""
Enhanced AV-DART Model with Standard Pretrained Encoders
使用标准预训练编码器（MViTv2 + VGGish）的增强版AV-DART模型
保持与原始DiffSal一致的预训练基础，同时集成Enhanced AV DART的创新设计
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
import sys
import os

# 添加路径以支持导入
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(os.path.dirname(os.path.dirname(current_dir)))
sys.path.insert(0, project_root)

from .standard_encoders import StandardEncoderInterface
from ..tokenizers.enhanced_av_tokenizer import EnhancedAVTokenizer
from .av_fusion_block import GatedFusion
from ..utils.sal_loss import avdart_loss_complete
from .saliency_decoder import SaliencyDecoder


class StandardEnhancedAVDARTModel(nn.Module):
    """
    Enhanced AV-DART模型 - 集成标准预训练编码器
    
    核心特性：
    1. 使用标准MViTv2 + VGGish预训练编码器（与原始DiffSal一致）
    2. 保持Enhanced AV DART的动态token分配和跨模态融合创新
    3. 支持DHF1K预训练 → 目标数据集微调的标准流程
    """
    
    def __init__(
        self,
        embed_dim: int = 768,
        total_tokens: int = 196,
        num_transformer_layers: int = 6,
        num_heads: int = 8,
        visual_size: int = 224,
        audio_size: tuple = (64, 224),
        output_size: tuple = (224, 384),
        token_strategy: str = "energy_based",
        
        # Enhanced功能开关
        use_pyramid_dart: bool = True,
        use_rope: bool = True,
        use_gated_fusion: bool = True,
        use_center_bias: bool = True,
        use_multiscale_supervision: bool = True,
        use_multi_metric_loss: bool = True,
        
        # 标准编码器配置
        freeze_pretrained_encoders: bool = False,
        visual_encoder_config = None,
        audio_encoder_config = None,
        
        # 其他参数
        **strategy_kwargs
    ):
        super().__init__()
        
        self.embed_dim = embed_dim
        self.total_tokens = total_tokens
        self.output_size = output_size
        self.use_gated_fusion = use_gated_fusion
        self.use_center_bias = use_center_bias
        self.use_multiscale_supervision = use_multiscale_supervision
        self.use_multi_metric_loss = use_multi_metric_loss
        
        # === 第一层：标准预训练编码器 ===
        # 与原始DiffSal保持完全一致的MViTv2 + VGGish编码器
        self.standard_encoders = StandardEncoderInterface(
            embed_dim=embed_dim,
            visual_config=visual_encoder_config,
            audio_config=audio_encoder_config,
            freeze_encoders=freeze_pretrained_encoders
        )
        
        # === 第二层：Enhanced AV DART处理层 ===
        # 在标准编码器基础上添加Enhanced AV DART的创新功能
        
        # 动态AV Tokenizer - 使用增强版
        self.av_tokenizer = EnhancedAVTokenizer(
            embed_dim=embed_dim,
            total_tokens=total_tokens,
            visual_size=visual_size,
            audio_size=audio_size,
            token_strategy=token_strategy,
            use_pyramid_dart=use_pyramid_dart,
            use_real_dart=False,  # 使用简化版DART，避免与标准编码器冲突
            **strategy_kwargs
        )
        
        # CLS token
        self.cls_token = nn.Parameter(torch.randn(1, 1, embed_dim))
        
        # === 第三层：Transformer编码器层 ===
        # 支持RoPE位置编码的增强Transformer
        if use_rope:
            from ..utils.rope_attention import RoPETransformerEncoder
            self.transformer = RoPETransformerEncoder(
                embed_dim=embed_dim,
                num_layers=num_transformer_layers,
                num_heads=num_heads,
                dropout=0.1,
                rope_theta=strategy_kwargs.get('rope_theta', 10000.0)
            )
        else:
            # 标准Transformer编码器
            encoder_layer = nn.TransformerEncoderLayer(
                d_model=embed_dim,
                nhead=num_heads,
                dim_feedforward=embed_dim * 4,
                dropout=0.1,
                activation='gelu',
                batch_first=True
            )
            self.transformer = nn.TransformerEncoder(
                encoder_layer,
                num_layers=num_transformer_layers
            )
        
        # === 第四层：跨模态融合模块 ===
        if use_gated_fusion:
            self.gated_fusion = GatedFusion(
                embed_dim=embed_dim,
                gate_type=strategy_kwargs.get('gate_type', 'se'),
                num_fusion_layers=strategy_kwargs.get('num_fusion_layers', 3)
            )
        
        # === 第五层：多尺度监督解码器 ===
        self.saliency_decoder = SaliencyDecoder(
            embed_dim=embed_dim,
            output_size=output_size,
            hidden_dim=strategy_kwargs.get('decoder_hidden_dim', 256),
            use_multiscale=use_multiscale_supervision,
            use_center_bias=use_center_bias,
            center_bias_type=strategy_kwargs.get('center_bias_type', 'learnable')
        )
        
        # === 损失函数 ===
        if use_multi_metric_loss:
            # 使用函数形式的损失计算
            self.loss_weights = {
                'kl_weight': strategy_kwargs.get('kl_weight', 1.0),
                'cc_weight': strategy_kwargs.get('cc_weight', 0.7),
                'sim_weight': strategy_kwargs.get('sim_weight', 0.5),
                'nss_weight': strategy_kwargs.get('nss_weight', 1.0)
            }
        
        # 参数初始化
        self._init_weights()
    
    def _init_weights(self):
        """初始化模型权重（不影响预训练编码器）"""
        for name, m in self.named_modules():
            # 跳过标准编码器的参数初始化
            if 'standard_encoders' in name:
                continue
                
            if isinstance(m, nn.Linear):
                nn.init.trunc_normal_(m.weight, std=0.02)
                if m.bias is not None:
                    nn.init.constant_(m.bias, 0)
            elif isinstance(m, nn.LayerNorm):
                nn.init.constant_(m.bias, 0)
                nn.init.constant_(m.weight, 1.0)
            elif isinstance(m, nn.Conv2d):
                nn.init.kaiming_normal_(m.weight, mode='fan_out', nonlinearity='relu')
                if m.bias is not None:
                    nn.init.constant_(m.bias, 0)
    
    def forward(self, visual_input, audio_input, return_attention=False):
        """
        前向传播
        Args:
            visual_input: [B, C, H, W] 或 [B, T, C, H, W]
            audio_input: [B, C, T, H, W] mel-spectrogram
            return_attention: 是否返回注意力权重
        Returns:
            saliency_map: [B, 1, H, W] 显著性预测
            aux_outputs: 辅助输出（多尺度监督等）
        """
        
        # === 步骤1：标准预训练编码器提取特征 ===
        encoder_features = self.standard_encoders(visual_input, audio_input)
        
        # 提取pooled特征作为tokenizer的输入
        visual_pooled = encoder_features['visual']['pooled_feature']  # [B, embed_dim]
        audio_pooled = encoder_features['audio']['pooled_feature']    # [B, embed_dim]
        
        # === 步骤2：特征预处理 - 将pooled特征转换为适合tokenizer的格式 ===
        # 将pooled特征reshape为假的空间特征图以供tokenizer处理
        B = visual_pooled.size(0)
        
        # 视觉特征：reshape为假的feature map [B, C, H, W]
        visual_for_tokenizer = visual_pooled.view(B, self.embed_dim, 1, 1)
        visual_for_tokenizer = F.interpolate(
            visual_for_tokenizer, size=(self.av_tokenizer.visual_size, self.av_tokenizer.visual_size), 
            mode='nearest'
        )
        
        # 音频特征：reshape为合适的格式
        audio_for_tokenizer = audio_pooled.view(B, 1, -1)  # [B, 1, embed_dim]
        
        # === 步骤3：Enhanced AV DART动态tokenization ===
        av_tokens, av_positions = self.av_tokenizer(visual_for_tokenizer, audio_for_tokenizer)
        
        # === 步骤4：添加CLS token ===
        cls_tokens = self.cls_token.expand(B, -1, -1)
        tokens = torch.cat([cls_tokens, av_tokens], dim=1)
        
        # === 步骤5：跨模态融合（如果启用）===
        if self.use_gated_fusion:
            # 分离视觉和音频tokens进行融合
            visual_tokens = av_tokens[:, :av_tokens.size(1)//2, :]
            audio_tokens = av_tokens[:, av_tokens.size(1)//2:, :]
            
            fused_visual, fused_audio = self.gated_fusion(visual_tokens, audio_tokens)
            av_tokens = torch.cat([fused_visual, fused_audio], dim=1)
            tokens = torch.cat([cls_tokens, av_tokens], dim=1)
        
        # === 步骤6：Transformer编码 ===
        encoded_tokens = self.transformer(tokens)
        
        # === 步骤7：分离CLS和patch tokens ===
        cls_feat = encoded_tokens[:, 0]  # [B, embed_dim]
        patch_feats = encoded_tokens[:, 1:]  # [B, N, embed_dim]
        
        # === 步骤8：显著性解码 ===
        saliency_outputs = self.saliency_decoder(patch_feats, cls_feat)
        
        # 处理输出格式
        if isinstance(saliency_outputs, dict):
            main_saliency = saliency_outputs['main']
            aux_outputs = {k: v for k, v in saliency_outputs.items() if k != 'main'}
        else:
            main_saliency = saliency_outputs
            aux_outputs = {}
        
        # 添加注意力权重（如果需要）
        if return_attention:
            aux_outputs['attention_weights'] = self._extract_attention_weights(encoded_tokens)
        
        # 添加编码器特征（用于分析）
        aux_outputs['encoder_features'] = encoder_features
        
        return main_saliency, aux_outputs
    
    def _extract_attention_weights(self, tokens):
        """提取注意力权重（用于可视化分析）"""
        # 这是一个简化版本，实际实现需要根据transformer类型调整
        return None
    
    def compute_loss(self, predictions, targets, aux_outputs=None):
        """
        计算损失
        Args:
            predictions: 主要预测结果
            targets: 目标显著性图
            aux_outputs: 辅助输出
        Returns:
            loss_dict: 损失字典
        """
        if not self.use_multi_metric_loss:
            # 使用标准KL散度损失
            kl_loss = F.kl_div(
                F.log_softmax(predictions.view(predictions.size(0), -1), dim=1),
                F.softmax(targets.view(targets.size(0), -1), dim=1),
                reduction='batchmean'
            )
            return {'total_loss': kl_loss, 'kl_loss': kl_loss}
        
        # 使用多指标损失
        loss_dict = self.criterion(predictions, targets)
        
        # 添加多尺度监督损失
        if self.use_multiscale_supervision and aux_outputs and 'multiscale_preds' in aux_outputs:
            multiscale_loss = 0
            for scale_pred in aux_outputs['multiscale_preds']:
                scale_target = F.interpolate(targets, size=scale_pred.shape[-2:], mode='bilinear', align_corners=False)
                scale_loss = self.criterion(scale_pred, scale_target)
                multiscale_loss += scale_loss['total_loss']
            
            loss_dict['multiscale_loss'] = multiscale_loss * 0.3  # 权重
            loss_dict['total_loss'] += loss_dict['multiscale_loss']
        
        return loss_dict
    
    def get_model_complexity(self):
        """获取模型复杂度信息"""
        total_params = sum(p.numel() for p in self.parameters())
        trainable_params = sum(p.numel() for p in self.parameters() if p.requires_grad)
        
        encoder_info = self.standard_encoders.get_encoder_info()
        
        complexity_info = {
            'model_type': 'Enhanced AV-DART with Standard Encoders',
            'total_parameters': total_params,
            'trainable_parameters': trainable_params,
            'embed_dim': self.embed_dim,
            'total_tokens': self.total_tokens,
            'transformer_layers': getattr(self.transformer, 'num_layers', len(self.transformer.layers)),
            'output_size': self.output_size,
            'encoder_info': encoder_info,
            'enhanced_features': {
                'pyramid_dart': self.av_tokenizer.use_pyramid_dart,
                'gated_fusion': self.use_gated_fusion,
                'center_bias': self.use_center_bias,
                'multiscale_supervision': self.use_multiscale_supervision,
                'multi_metric_loss': self.use_multi_metric_loss
            }
        }
        
        return complexity_info
    
    def set_training_mode(self, mode='dhf1k_pretrain'):
        """
        设置训练模式
        Args:
            mode: 'dhf1k_pretrain' 或 'target_finetune'
        """
        if mode == 'dhf1k_pretrain':
            # DHF1K预训练阶段：冻结预训练编码器，训练其他部分
            for param in self.standard_encoders.parameters():
                param.requires_grad = False
            self.standard_encoders.eval()
            
            # 其他模块训练
            for name, module in self.named_modules():
                if 'standard_encoders' not in name:
                    module.train()
                    for param in module.parameters():
                        param.requires_grad = True
            
            print("设置为DHF1K预训练模式：冻结MViTv2+VGGish，训练Enhanced AV-DART层")
            
        elif mode == 'target_finetune':
            # 目标数据集微调：解冻所有参数进行端到端微调
            for param in self.parameters():
                param.requires_grad = True
            self.train()
            
            print("设置为目标数据集微调模式：端到端训练所有参数")
        
        else:
            raise ValueError(f"不支持的训练模式: {mode}")
    
    def get_training_statistics(self):
        """获取训练统计信息"""
        total_params = sum(p.numel() for p in self.parameters())
        trainable_params = sum(p.numel() for p in self.parameters() if p.requires_grad)
        frozen_params = total_params - trainable_params
        
        encoder_params = sum(p.numel() for p in self.standard_encoders.parameters())
        dart_params = sum(p.numel() for p in self.av_tokenizer.parameters())
        transformer_params = sum(p.numel() for p in self.transformer.parameters())
        decoder_params = sum(p.numel() for p in self.saliency_decoder.parameters())
        
        return {
            'total_parameters': total_params,
            'trainable_parameters': trainable_params,
            'frozen_parameters': frozen_params,
            'trainable_ratio': trainable_params / total_params,
            'module_breakdown': {
                'encoders': encoder_params,
                'av_tokenizer': dart_params,
                'transformer': transformer_params,
                'decoder': decoder_params
            }
        }


def create_standard_enhanced_av_dart(**kwargs):
    """创建标准Enhanced AV-DART模型的便捷函数"""
    return StandardEnhancedAVDARTModel(**kwargs)


def create_dhf1k_pretrain_model(**kwargs):
    """创建DHF1K预训练模型配置"""
    return StandardEnhancedAVDARTModel(
        embed_dim=768,
        total_tokens=196,
        num_transformer_layers=6,
        num_heads=8,
        freeze_pretrained_encoders=True,  # DHF1K预训练时冻结编码器
        use_pyramid_dart=True,
        use_rope=True,
        use_gated_fusion=True,
        use_center_bias=True,
        use_multiscale_supervision=True,
        use_multi_metric_loss=True,
        **kwargs
    )


def create_target_finetune_model(**kwargs):
    """创建目标数据集微调模型配置"""
    return StandardEnhancedAVDARTModel(
        embed_dim=768,
        total_tokens=196,
        num_transformer_layers=6,
        num_heads=8,
        freeze_pretrained_encoders=False,  # 微调时解冻所有参数
        use_pyramid_dart=True,
        use_rope=True,
        use_gated_fusion=True,
        use_center_bias=True,
        use_multiscale_supervision=True,
        use_multi_metric_loss=True,
        **kwargs
    )