"""
Pyramid DART: Multi-scale Visual Tokenizer with Top-K Selection
多尺度视觉分词器，结合SPN进行Top-K选择，提供更丰富的尺度信息
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import math
import sys
import os

# 添加路径以支持导入
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(os.path.dirname(os.path.dirname(current_dir)))
sys.path.insert(0, project_root)

from DART.av_dart.utils.spn import ScorePredNetV1


class PyramidDART(nn.Module):
    """
    Pyramid DART: 多尺度视觉分词器
    
    并联3个尺度的卷积投影：Conv8/8、Conv16/16、Conv32/32
    使用SPN对所有tokens统一打分后进行Top-K选择
    """
    
    def __init__(self, embed_dim=768, k_total=256, input_channels=3, use_gumbel=False):
        super().__init__()
        
        self.embed_dim = embed_dim
        self.k_total = k_total
        self.use_gumbel = use_gumbel
        
        # 3个尺度的投影层
        self.proj8 = nn.Conv2d(input_channels, embed_dim, kernel_size=8, stride=8)
        self.proj16 = nn.Conv2d(input_channels, embed_dim, kernel_size=16, stride=16)
        self.proj32 = nn.Conv2d(input_channels, embed_dim, kernel_size=32, stride=32)
        
        # 统一的SPN打分网络
        # 创建一个简单的特征提取器用于SPN
        self.score_features = nn.Sequential(
            nn.Conv2d(input_channels, 64, kernel_size=3, stride=1, padding=1),
            nn.ReLU(),
            nn.Conv2d(64, 96, kernel_size=3, stride=2, padding=1),
            nn.ReLU(),
            nn.AdaptiveAvgPool2d((14, 14))  # 统一到14x14大小
        )
        
        # SPN评分网络
        self.spn = ScorePredNetV1(
            features=self.score_features,
            feature_dim=96,
            hidden_dim=embed_dim
        )
        
        # 温度参数用于Gumbel-Top-K
        self.temperature = nn.Parameter(torch.ones(1) * 1.0)
        
        # 位置编码（每个尺度独立）
        self.pos_embed_8 = nn.Parameter(torch.randn(1, 784, embed_dim) * 0.02)   # 28x28
        self.pos_embed_16 = nn.Parameter(torch.randn(1, 196, embed_dim) * 0.02)  # 14x14
        self.pos_embed_32 = nn.Parameter(torch.randn(1, 49, embed_dim) * 0.02)   # 7x7
        
        # 尺度标识嵌入
        self.scale_embed = nn.Embedding(3, embed_dim)
        
        self._init_weights()
    
    def _init_weights(self):
        """初始化权重"""
        for m in self.modules():
            if isinstance(m, nn.Conv2d):
                nn.init.kaiming_normal_(m.weight, mode='fan_out', nonlinearity='relu')
                if m.bias is not None:
                    nn.init.constant_(m.bias, 0)
            elif isinstance(m, nn.Linear):
                nn.init.trunc_normal_(m.weight, std=0.02)
                if m.bias is not None:
                    nn.init.constant_(m.bias, 0)
    
    def gumbel_topk(self, scores, k, temperature=1.0):
        """
        可微分的Gumbel Top-K选择
        
        Args:
            scores: [B, N, 1] - token重要性分数
            k: int - 选择的token数量
            temperature: float - Gumbel温度参数
            
        Returns:
            indices: [B, k] - 选中的token索引
            mask: [B, N] - 选择掩码
        """
        B, N, _ = scores.shape
        
        # 添加Gumbel噪声
        gumbel_noise = -torch.log(-torch.log(torch.rand_like(scores.squeeze(-1)) + 1e-8) + 1e-8)
        noisy_scores = (scores.squeeze(-1) + gumbel_noise) / temperature
        
        # Top-K选择
        topk_values, topk_indices = torch.topk(noisy_scores, k=k, dim=1)
        
        # 创建选择掩码
        mask = torch.zeros_like(noisy_scores)
        mask.scatter_(1, topk_indices, 1.0)
        
        return topk_indices, mask
    
    def forward(self, x, num_patches=None):
        """
        前向传播
        
        Args:
            x: [B, C, H, W] - 输入图像
            num_patches: int - 目标token数量（兼容原DART接口）
            
        Returns:
            tokens: [B, k_total, embed_dim] - 选中的tokens
            pos_embed: [B, k_total, embed_dim] - 对应的位置编码
        """
        if num_patches is not None:
            self.k_total = num_patches
            
        B, C, H, W = x.shape
        device = x.device
        
        # 确保输入尺寸能被各个patch size整除
        if H != 224 or W != 224:
            x = F.interpolate(x, size=(224, 224), mode='bilinear', align_corners=False)
        
        # 多尺度投影
        tokens_list = []
        pos_embeds_list = []
        scale_ids = []
        
        # 尺度1: 8x8 patches -> 28x28 = 784 tokens
        feat8 = self.proj8(x)  # [B, embed_dim, 28, 28]
        tokens8 = feat8.flatten(2).transpose(1, 2)  # [B, 784, embed_dim]
        pos8 = self.pos_embed_8.expand(B, -1, -1)
        tokens_list.append(tokens8)
        pos_embeds_list.append(pos8)
        scale_ids.extend([0] * tokens8.size(1))
        
        # 尺度2: 16x16 patches -> 14x14 = 196 tokens  
        feat16 = self.proj16(x)  # [B, embed_dim, 14, 14]
        tokens16 = feat16.flatten(2).transpose(1, 2)  # [B, 196, embed_dim]
        pos16 = self.pos_embed_16.expand(B, -1, -1)
        tokens_list.append(tokens16)
        pos_embeds_list.append(pos16)
        scale_ids.extend([1] * tokens16.size(1))
        
        # 尺度3: 32x32 patches -> 7x7 = 49 tokens
        feat32 = self.proj32(x)  # [B, embed_dim, 7, 7]
        tokens32 = feat32.flatten(2).transpose(1, 2)  # [B, 49, embed_dim]
        pos32 = self.pos_embed_32.expand(B, -1, -1)
        tokens_list.append(tokens32)
        pos_embeds_list.append(pos32)
        scale_ids.extend([2] * tokens32.size(1))
        
        # 合并所有尺度的tokens
        all_tokens = torch.cat(tokens_list, dim=1)  # [B, 784+196+49, embed_dim]
        all_pos_embeds = torch.cat(pos_embeds_list, dim=1)  # [B, 1029, embed_dim]
        
        # 添加尺度标识嵌入
        scale_ids_tensor = torch.tensor(scale_ids, device=device).long()
        scale_embeds = self.scale_embed(scale_ids_tensor).unsqueeze(0).expand(B, -1, -1)
        all_tokens = all_tokens + scale_embeds
        
        # 使用SPN计算重要性分数
        # 注意：SPN期望输入是图像，我们使用原始输入
        scores = self.spn(x, shape=(all_tokens.size(1),))  # [B, total_tokens]
        scores = scores.unsqueeze(-1)  # [B, total_tokens, 1]
        
        # Top-K选择
        if self.use_gumbel and self.training:
            # 训练时使用可微分的Gumbel Top-K
            topk_indices, selection_mask = self.gumbel_topk(
                scores, self.k_total, self.temperature.item()
            )
        else:
            # 推理时使用确定性Top-K
            topk_indices = torch.topk(scores.squeeze(-1), k=self.k_total, dim=1).indices
            selection_mask = torch.zeros_like(scores.squeeze(-1))
            selection_mask.scatter_(1, topk_indices, 1.0)
        
        # 选择对应的tokens和位置编码
        gather_idx = topk_indices.unsqueeze(-1).expand(-1, -1, self.embed_dim)
        selected_tokens = torch.gather(all_tokens, 1, gather_idx)
        selected_pos_embeds = torch.gather(all_pos_embeds, 1, gather_idx)
        
        return selected_tokens, selected_pos_embeds
    
    def get_scale_statistics(self, x):
        """
        获取各尺度的统计信息（用于分析）
        
        Returns:
            stats: dict - 包含各尺度tokens数量和重要性分布
        """
        with torch.no_grad():
            self.eval()
            
            B, C, H, W = x.shape
            
            # 计算重要性分数
            scores = self.spn(x, shape=(1029,))  # 784+196+49
            
            # 分解各尺度的分数
            scale8_scores = scores[:, :784]
            scale16_scores = scores[:, 784:784+196] 
            scale32_scores = scores[:, 784+196:]
            
            # Top-K选择
            topk_indices = torch.topk(scores, k=self.k_total, dim=1).indices
            
            # 统计各尺度被选中的token数量
            scale8_selected = ((topk_indices >= 0) & (topk_indices < 784)).sum().item()
            scale16_selected = ((topk_indices >= 784) & (topk_indices < 784+196)).sum().item()
            scale32_selected = (topk_indices >= 784+196).sum().item()
            
            stats = {
                'scale8_tokens': 784,
                'scale16_tokens': 196,
                'scale32_tokens': 49,
                'scale8_selected': scale8_selected,
                'scale16_selected': scale16_selected,
                'scale32_selected': scale32_selected,
                'scale8_avg_score': scale8_scores.mean().item(),
                'scale16_avg_score': scale16_scores.mean().item(),
                'scale32_avg_score': scale32_scores.mean().item(),
                'selection_ratio': {
                    'scale8': scale8_selected / 784,
                    'scale16': scale16_selected / 196,
                    'scale32': scale32_selected / 49
                }
            }
            
            return stats


def create_pyramid_dart(embed_dim=768, k_total=256, input_channels=3, use_gumbel=False):
    """创建Pyramid DART的便捷函数"""
    return PyramidDART(
        embed_dim=embed_dim,
        k_total=k_total,
        input_channels=input_channels,
        use_gumbel=use_gumbel
    )


# 预定义配置
def create_pyramid_dart_small(**kwargs):
    """小型Pyramid DART"""
    return create_pyramid_dart(
        embed_dim=384,
        k_total=144,
        **kwargs
    )


def create_pyramid_dart_base(**kwargs):
    """基础Pyramid DART"""
    return create_pyramid_dart(
        embed_dim=768,
        k_total=196,
        **kwargs
    )


def create_pyramid_dart_large(**kwargs):
    """大型Pyramid DART"""
    return create_pyramid_dart(
        embed_dim=1024,
        k_total=256,
        **kwargs
    )