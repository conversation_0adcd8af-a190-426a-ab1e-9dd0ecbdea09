"""
标准化预训练编码器模块
使用与原始DiffSal相同的MViTv2 + VGGish预训练模型组合
延续 Kinetics + AudioSet + DHF1K 的预训练方式
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import sys
import os

# 添加路径以支持导入
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(os.path.dirname(os.path.dirname(current_dir)))
sys.path.insert(0, project_root)

from models.mvit import MViT
from models.vggish import VGGish


class StandardVisualEncoder(nn.Module):
    """
    标准化视觉编码器 - 使用预训练MViTv2
    延续原始DiffSal的Kinetics预训练 + DHF1K显著性预训练方式
    """
    
    def __init__(
        self,
        arch: str = "small",
        pretrained_path: str = "data/pretrained_models/mvit-small-p244_32xb16-16x4x1-200e_kinetics400-rgb_20230201-23284ff3.pth",
        out_scales: list = [0, 1, 2, 3],
        embed_dim: int = 768,
        freeze_backbone: bool = False
    ):
        super().__init__()
        
        # 核心MViT编码器 - 与原始DiffSal完全一致
        self.mvit_encoder = MViT(
            arch=arch,
            pretrained=pretrained_path,
            out_scales=out_scales
        )
        
        self.embed_dim = embed_dim
        self.out_scales = out_scales
        
        # 特征投影层 - 将MViT多尺度特征投影到统一维度
        self.feature_projectors = nn.ModuleDict()
        mvit_dims = [96, 192, 384, 768]  # MViT small的通道数
        
        for i, scale in enumerate(out_scales):
            input_dim = mvit_dims[scale]
            self.feature_projectors[f'scale_{scale}'] = nn.Sequential(
                nn.Conv2d(input_dim, embed_dim, kernel_size=1),
                nn.BatchNorm2d(embed_dim),  # 使用BatchNorm2d而不是LayerNorm
                nn.GELU()
            )
        
        # 时间池化层
        self.temporal_pool = nn.AdaptiveAvgPool3d((1, None, None))
        
        # 是否冻结backbone
        if freeze_backbone:
            self._freeze_backbone()
    
    def _freeze_backbone(self):
        """冻结MViT backbone参数"""
        for param in self.mvit_encoder.parameters():
            param.requires_grad = False
        self.mvit_encoder.eval()
    
    def forward(self, visual_input):
        """
        前向传播
        Args:
            visual_input: [B, C, T, H, W] 或 [B, C, H, W]
        Returns:
            features: 字典，包含多尺度特征和pooled特征
        """
        # 确保输入格式正确
        if visual_input.dim() == 4:  # [B, C, H, W]
            # 扩展时间维度以匹配MViT期望的5D输入
            visual_input = visual_input.unsqueeze(2)  # [B, C, 1, H, W]
        elif visual_input.dim() == 5:  # [B, C, T, H, W] or [B, T, C, H, W]
            if visual_input.size(2) in [1, 3] and visual_input.size(1) > 16:
                # 可能是 [B, T, C, H, W] 格式，转换为 [B, C, T, H, W]
                visual_input = visual_input.permute(0, 2, 1, 3, 4)
        
        # MViT前向传播
        mvit_features = self.mvit_encoder(visual_input)  # 返回多尺度特征列表
        
        # 投影多尺度特征到统一维度
        projected_features = {}
        pooled_features = []
        
        for i, (scale, feat) in enumerate(zip(self.out_scales, mvit_features)):
            # feat shape: [B, C, T, H, W]
            B, C, T, H, W = feat.shape
            
            # 时间维度聚合（平均池化）
            feat_temporal_pooled = feat.mean(dim=2)  # [B, C, H, W]
            
            # 投影到统一维度
            projected_feat = self.feature_projectors[f'scale_{scale}'](feat_temporal_pooled)
            projected_features[f'scale_{scale}'] = projected_feat
            
            # 空间池化得到全局特征
            global_feat = F.adaptive_avg_pool2d(projected_feat, (1, 1))  # [B, embed_dim, 1, 1]
            global_feat = global_feat.flatten(1)  # [B, embed_dim]
            pooled_features.append(global_feat)
        
        # 最终的pooled特征：多尺度特征的加权平均
        final_pooled = torch.stack(pooled_features, dim=1).mean(dim=1)  # [B, embed_dim]
        
        return {
            'multi_scale_features': projected_features,
            'pooled_feature': final_pooled,
            'raw_features': mvit_features
        }


class StandardAudioEncoder(nn.Module):
    """
    标准化音频编码器 - 使用预训练VGGish
    延续原始DiffSal的AudioSet预训练方式
    """
    
    def __init__(
        self,
        pretrained: bool = True,
        embed_dim: int = 768,
        freeze_backbone: bool = False
    ):
        super().__init__()
        
        # 核心VGGish编码器 - 与原始DiffSal完全一致
        self.vggish_encoder = VGGish(pretrained=pretrained)
        
        self.embed_dim = embed_dim
        
        # VGGish输出128维嵌入，投影到统一维度
        self.feature_projector = nn.Sequential(
            nn.Linear(128, embed_dim),
            nn.LayerNorm(embed_dim),
            nn.GELU()
        )
        
        # 特征图投影器（用于处理VGGish的卷积特征）
        # VGGish最后一层卷积输出为512通道
        self.conv_projector = nn.Sequential(
            nn.Conv2d(512, embed_dim, kernel_size=1),
            nn.AdaptiveAvgPool2d((1, 1)),
            nn.Flatten()
        )
        
        # 是否冻结backbone
        if freeze_backbone:
            self._freeze_backbone()
    
    def _freeze_backbone(self):
        """冻结VGGish backbone参数"""
        for param in self.vggish_encoder.parameters():
            param.requires_grad = False
        self.vggish_encoder.eval()
    
    def forward(self, audio_input):
        """
        前向传播
        Args:
            audio_input: [B, C, T, H, W] mel-spectrogram
        Returns:
            features: 字典，包含嵌入特征和卷积特征
        """
        # 处理输入维度
        if audio_input.dim() == 5:  # [B, C, T, H, W]
            B, C, T, H, W = audio_input.shape
            # VGGish期望4D输入 [B*T, C, H, W]
            audio_input = audio_input.view(B*T, C, H, W)
        elif audio_input.dim() == 4:  # [B, C, H, W]
            B, T = audio_input.size(0), 1
        elif audio_input.dim() == 3:  # [B, H, W] - 单通道
            audio_input = audio_input.unsqueeze(1)  # [B, C, H, W]
            B, T = audio_input.size(0), 1
        else:
            raise ValueError(f"不支持的音频输入维度: {audio_input.shape}")
        
        # VGGish前向传播
        conv_features, embedding_features = self.vggish_encoder(audio_input)
        
        # conv_features: [B*T, 512, H', W']
        # embedding_features: [B*T, 128]
        
        # 投影嵌入特征
        projected_embedding = self.feature_projector(embedding_features)  # [B*T, embed_dim]
        
        # 投影卷积特征
        projected_conv = self.conv_projector(conv_features)  # [B*T, embed_dim]
        
        # 时间维度重组和聚合
        if T > 1:
            projected_embedding = projected_embedding.view(B, T, -1).mean(dim=1)  # [B, embed_dim]
            projected_conv = projected_conv.view(B, T, -1).mean(dim=1)  # [B, embed_dim]
        else:
            projected_embedding = projected_embedding.view(B, -1)
            projected_conv = projected_conv.view(B, -1)
        
        return {
            'embedding_feature': projected_embedding,
            'conv_feature': projected_conv,
            'pooled_feature': (projected_embedding + projected_conv) / 2,  # 融合特征
            'raw_conv_features': conv_features,
            'raw_embedding_features': embedding_features
        }


class StandardEncoderInterface(nn.Module):
    """
    标准编码器接口 - 统一视觉和音频编码器
    为Enhanced AV DART提供标准化的特征提取接口
    """
    
    def __init__(
        self,
        embed_dim: int = 768,
        visual_config: dict = None,
        audio_config: dict = None,
        freeze_encoders: bool = False
    ):
        super().__init__()
        
        self.embed_dim = embed_dim
        
        # 默认配置
        if visual_config is None:
            visual_config = {
                'arch': 'small',
                'pretrained_path': 'data/pretrained_models/mvit-small-p244_32xb16-16x4x1-200e_kinetics400-rgb_20230201-23284ff3.pth',
                'out_scales': [0, 1, 2, 3],
                'freeze_backbone': freeze_encoders
            }
        
        if audio_config is None:
            audio_config = {
                'pretrained': True,
                'freeze_backbone': freeze_encoders
            }
        
        # 初始化编码器
        self.visual_encoder = StandardVisualEncoder(
            embed_dim=embed_dim,
            **visual_config
        )
        
        self.audio_encoder = StandardAudioEncoder(
            embed_dim=embed_dim,
            **audio_config
        )
        
    def forward(self, visual_input, audio_input):
        """
        联合前向传播
        Returns:
            features: 字典，包含视觉和音频的各种特征表示
        """
        visual_features = self.visual_encoder(visual_input)
        audio_features = self.audio_encoder(audio_input)
        
        return {
            'visual': visual_features,
            'audio': audio_features,
            'joint_pooled': (visual_features['pooled_feature'] + audio_features['pooled_feature']) / 2
        }
    
    def get_encoder_info(self):
        """获取编码器信息"""
        return {
            'visual_encoder': 'MViTv2 (Kinetics pretrained)',
            'audio_encoder': 'VGGish (AudioSet pretrained)', 
            'embed_dim': self.embed_dim,
            'total_params': sum(p.numel() for p in self.parameters()),
            'trainable_params': sum(p.numel() for p in self.parameters() if p.requires_grad)
        }


def create_standard_encoders(embed_dim=768, freeze_encoders=False, **kwargs):
    """创建标准编码器的便捷函数"""
    return StandardEncoderInterface(
        embed_dim=embed_dim,
        freeze_encoders=freeze_encoders,
        **kwargs
    )