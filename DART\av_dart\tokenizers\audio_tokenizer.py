"""
音频分支动态Tokenizer

基于DART的音频tokenizer，支持：
- log-mel谱图的自适应分块
- 时-频域内容感知采样
- 可变token数量预算
- 音频能量分析和特征提取
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Optional, Tuple, Union, Dict
import sys
import os
import math

# 添加相对路径
sys.path.append(os.path.dirname(__file__))
from ..adapters.dart_adapter import AudioDARTAdapter


class AudioDARTTokenizer(nn.Module):
    """
    音频DART Tokenizer
    
    核心功能：
    1. 接收log-mel谱图 [B, 1, T, F] 
    2. 基于时-频域重要性自适应分块
    3. 支持动态token数量（由预算控制器分配）
    4. 提取音频能量特征用于跨模态门控
    
    Args:
        input_size: 输入谱图尺寸 (T, F)，默认(64, 224)
        patch_size: patch大小，默认16
        embed_dim: 输出嵌入维度，默认768
        scorer_type: 评分网络类型
        enable_energy_extraction: 是否启用能量提取
        mel_bins: mel频率bins数量
        hop_length: STFT的hop长度
        sample_rate: 音频采样率
    """
    
    def __init__(
        self,
        input_size: Tuple[int, int] = (64, 224),  # (T, F)
        patch_size: int = 16,
        embed_dim: int = 768,
        scorer_type: str = "mobilenet_small",
        enable_energy_extraction: bool = True,
        mel_bins: int = 224,
        hop_length: int = 512,
        sample_rate: int = 22050,
        **kwargs
    ):
        super().__init__()
        
        self.input_size = input_size  # (T, F)
        self.patch_size = patch_size
        self.embed_dim = embed_dim
        self.enable_energy_extraction = enable_energy_extraction
        self.mel_bins = mel_bins
        self.hop_length = hop_length
        self.sample_rate = sample_rate
        
        # DART适配器
        self.dart_adapter = AudioDARTAdapter(
            input_size=input_size,
            patch_size=patch_size,
            embed_dim=embed_dim,
            scorer_type=scorer_type,
            **kwargs
        )
        
        # 音频预处理模块
        self.audio_preprocess = AudioPreprocessor(
            mel_bins=mel_bins,
            hop_length=hop_length,
            sample_rate=sample_rate
        )
        
        # 能量提取器
        if enable_energy_extraction:
            self.energy_extractor = AudioEnergyExtractor(input_size[0])  # T
        
        # 音频特定的位置编码
        self.pos_encoding_type = "fourier"  # "fourier", "learned", "sinusoidal"
        if self.pos_encoding_type == "learned":
            max_patches = self.dart_adapter.num_patches_default * 2
            self.pos_embed = nn.Parameter(torch.zeros(1, max_patches, embed_dim))
            nn.init.trunc_normal_(self.pos_embed, std=0.02)
        elif self.pos_encoding_type == "fourier":
            self.fourier_pos_enc = FourierPositionalEncoding(embed_dim)
    
    def forward(
        self,
        audio_input: Union[torch.Tensor, Dict],
        num_patches: int,
        return_dict: bool = False
    ) -> Union[Tuple[torch.Tensor, torch.Tensor, Optional[torch.Tensor]], Dict]:
        """
        前向传播
        
        Args:
            audio_input: 音频输入，可以是：
                - log-mel谱图 [B, 1, T, F]
                - 原始音频波形 [B, L] (将自动转换为谱图)
                - 包含多种音频信息的字典
                - 错误格式的视频数据 (将自动处理)
            num_patches: 分配的音频token数量 (N_a)
            return_dict: 是否返回详细信息
            
        Returns:
            audio_tokens: 音频token [B, N_a, D]
            pos_encoding: 位置编码 [B, N_a, D]
            audio_energy: 音频能量 [B, T] (如果启用)
            或包含详细信息的字典
        """
        # 处理输入 - 自动检测和修复格式
        spec = self._preprocess_audio_input(audio_input)
        
        B = spec.size(0)
        
        # 验证谱图格式
        assert spec.dim() == 4 and spec.size(1) == 1, f"Expected [B,1,T,F], got {spec.shape}"
        
        # 动态tokenization
        result = self.dart_adapter(
            spec,
            num_patches=num_patches,
            return_dict=True
        )
        
        audio_tokens = result['x']  # [B, N_a, D]
        
        # 位置编码
        if self.pos_encoding_type == "learned":
            pos_encoding = self.pos_embed[:, :num_patches, :].expand(B, -1, -1)
        elif self.pos_encoding_type == "fourier":
            # 使用频域特定的位置编码
            pos_encoding = self.fourier_pos_enc(audio_tokens, result.get('new_edges', None))
        else:
            # 使用DART提供的位置信息或生成正弦位置编码
            if 'pos' in result:
                pos_encoding = result['pos']
            else:
                pos_encoding = self.generate_sinusoidal_pos_encoding(audio_tokens)
        
        # 提取音频能量特征
        audio_energy = None
        if self.enable_energy_extraction:
            audio_energy = self.energy_extractor(spec)  # [B, T]
        
        if return_dict:
            output = {
                'audio_tokens': audio_tokens,
                'pos_encoding': pos_encoding,
                'audio_energy': audio_energy,
                'num_patches': num_patches,
                'spectrogram': spec,
                'importance_map': result.get('score', None),
                'patch_positions': result.get('new_edges', None)
            }
            return output
        else:
            return audio_tokens, pos_encoding, audio_energy
    
    def _preprocess_audio_input(self, audio_input: Union[torch.Tensor, Dict]) -> torch.Tensor:
        """
        预处理音频输入，自动检测和修复格式问题
        
        Args:
            audio_input: 各种格式的音频输入
            
        Returns:
            spec: 标准化的谱图 [B, 1, T, F]
        """
        if isinstance(audio_input, dict):
            spectrogram = audio_input.get('spectrogram', None)
            waveform = audio_input.get('waveform', None)
            if spectrogram is not None:
                spec = spectrogram
            elif waveform is not None:
                spec = self.audio_preprocess.waveform_to_spectrogram(waveform)
            else:
                raise ValueError("audio_input dict must contain 'spectrogram' or 'waveform'")
        else:
            spec = self._handle_tensor_input(audio_input)
        
        # 验证和修复谱图格式
        spec = self._validate_and_fix_spectrogram(spec)
        return spec
    
    def _handle_tensor_input(self, audio_input: torch.Tensor) -> torch.Tensor:
        """
        处理张量格式的音频输入（现在应该已经是标准格式）
        
        Args:
            audio_input: 张量形式的音频数据
            
        Returns:
            spec: 谱图 [B, 1, T, F]
        """
        shape = audio_input.shape
        
        if audio_input.dim() == 2:  # 原始波形 [B, L]
            spec = self.audio_preprocess.waveform_to_spectrogram(audio_input)
            
        elif audio_input.dim() == 3:  # [B, T, F] 格式 - 新增支持
            # [B, T, F] -> [B, 1, T, F] (增加通道维度)
            spec = audio_input.unsqueeze(1)
            
        elif audio_input.dim() == 4:  # 标准谱图格式 [B, 1, T, F]
            # 现在应该已经是正确的格式
            if shape[1] == 1:  # [B, 1, T, F]
                spec = audio_input
            else:
                raise ValueError(f"Unexpected 4D audio format: {shape}. Expected [B, 1, T, F]")
                
        else:
            raise ValueError(f"Unsupported audio input shape: {shape}. Expected [B, L], [B, T, F], or [B, 1, T, F]")
        
        return spec
    
    def _validate_and_fix_spectrogram(self, spec: torch.Tensor) -> torch.Tensor:
        """
        验证和修复谱图格式
        
        Args:
            spec: 输入谱图
            
        Returns:
            fixed_spec: 修复后的谱图 [B, 1, T, F]
        """
        if spec.dim() != 4:
            raise ValueError(f"谱图必须是4维张量，得到 {spec.dim()}维: {spec.shape}")
        
        B, C, T, F = spec.shape
        
        # 检查通道数
        if C != 1:
            spec = spec.mean(dim=1, keepdim=True)
        
        # 检查时间和频率维度的合理性
        if T < 8:  # 时间维度太小
            repeat_factor = max(1, 8 // T)
            spec = spec.repeat(1, 1, repeat_factor, 1)
            T = spec.shape[2]
        
        if F < 32:  # 频率维度太小
            spec = torch.nn.functional.interpolate(spec, size=(T, max(F, 64)), mode='bilinear', align_corners=False)
            F = spec.shape[3]
        
        # 调整到标准尺寸
        target_T, target_F = self.input_size
        if (T, F) != (target_T, target_F):
            spec = torch.nn.functional.interpolate(spec, size=(target_T, target_F), mode='bilinear', align_corners=False)
        
        return spec
    
    def generate_sinusoidal_pos_encoding(self, tokens: torch.Tensor) -> torch.Tensor:
        """
        生成正弦位置编码
        
        Args:
            tokens: [B, N, D]
            
        Returns:
            pos_encoding: [B, N, D]
        """
        B, N, D = tokens.shape
        
        # 位置索引
        position = torch.arange(N, dtype=torch.float32, device=tokens.device).unsqueeze(1)
        
        # 频率维度
        div_term = torch.exp(torch.arange(0, D, 2, dtype=torch.float32, device=tokens.device) *
                            -(math.log(10000.0) / D))
        
        pos_encoding = torch.zeros(N, D, device=tokens.device)
        pos_encoding[:, 0::2] = torch.sin(position * div_term)
        pos_encoding[:, 1::2] = torch.cos(position * div_term)
        
        return pos_encoding.unsqueeze(0).expand(B, -1, -1)  # [B, N, D]
    
    def get_num_patches_default(self) -> int:
        """获取默认patch数量"""
        return self.dart_adapter.num_patches_default
    
    def get_grid_size(self) -> Tuple[int, int]:
        """获取网格尺寸 (T_patches, F_patches)"""
        return self.dart_adapter.grid_size


class AudioPreprocessor(nn.Module):
    """
    音频预处理模块
    
    负责将原始音频波形转换为log-mel谱图
    """
    
    def __init__(
        self,
        mel_bins: int = 224,
        hop_length: int = 512,
        sample_rate: int = 22050,
        n_fft: int = 2048,
        fmin: float = 0.0,
        fmax: Optional[float] = None
    ):
        super().__init__()
        
        self.mel_bins = mel_bins
        self.hop_length = hop_length
        self.sample_rate = sample_rate
        self.n_fft = n_fft
        self.fmin = fmin
        self.fmax = fmax or sample_rate // 2
        
        # Mel滤波器组
        self.register_buffer('mel_filters', self._create_mel_filters())
    
    def _create_mel_filters(self) -> torch.Tensor:
        """创建Mel滤波器组"""
        # Mel滤波器组
        return torch.randn(self.mel_bins, self.n_fft // 2 + 1)  # placeholder
    
    def waveform_to_spectrogram(self, waveform: torch.Tensor) -> torch.Tensor:
        """
        将波形转换为log-mel谱图
        
        Args:
            waveform: [B, L] 原始音频波形
            
        Returns:
            spectrogram: [B, 1, T, F] log-mel谱图
        """
        B, L = waveform.shape
        
        # STFT实现
        # 这里使用占位实现
        T = L // self.hop_length
        spectrogram = torch.randn(B, 1, T, self.mel_bins, device=waveform.device)
        
        return spectrogram
    
    def forward(self, waveform: torch.Tensor) -> torch.Tensor:
        """前向传播"""
        return self.waveform_to_spectrogram(waveform)


class AudioEnergyExtractor(nn.Module):
    """
    音频能量提取器
    
    从谱图中提取时序音频能量特征，用于跨模态门控
    """
    
    def __init__(
        self,
        temporal_len: int,
        energy_smooth_kernel: int = 3
    ):
        super().__init__()
        
        self.temporal_len = temporal_len
        self.energy_smooth_kernel = energy_smooth_kernel
        
        # 能量提取卷积层
        self.energy_conv = nn.Sequential(
            nn.Conv2d(1, 16, kernel_size=(3, 3), padding=1),
            nn.ReLU(),
            nn.AdaptiveAvgPool2d((temporal_len, 1)),  # 池化到时序维度
            nn.Conv2d(16, 1, kernel_size=1)
        )
        
        # 时序平滑
        if energy_smooth_kernel > 1:
            self.smooth_conv = nn.Conv1d(
                1, 1, 
                kernel_size=energy_smooth_kernel, 
                padding=energy_smooth_kernel // 2
            )
        else:
            self.smooth_conv = nn.Identity()
    
    def forward(self, spectrogram: torch.Tensor) -> torch.Tensor:
        """
        提取音频能量
        
        Args:
            spectrogram: [B, 1, T, F] log-mel谱图
            
        Returns:
            energy: [B, T] 时序能量特征
        """
        B = spectrogram.size(0)
        
        # 提取能量特征
        energy = self.energy_conv(spectrogram)  # [B, 1, T, 1]
        energy = energy.squeeze(-1).squeeze(1)  # [B, T]
        
        # 时序平滑
        if hasattr(self.smooth_conv, 'weight'):  # 不是Identity
            energy = energy.unsqueeze(1)  # [B, 1, T]
            energy = self.smooth_conv(energy)
            energy = energy.squeeze(1)  # [B, T]
        
        # 归一化到 [0, 1]
        energy = torch.sigmoid(energy)
        
        return energy


class FourierPositionalEncoding(nn.Module):
    """
    傅里叶位置编码
    
    专门为音频谱图设计的位置编码，考虑时-频域特性
    """
    
    def __init__(self, embed_dim: int, max_freq: float = 10000.0):
        super().__init__()
        
        self.embed_dim = embed_dim
        self.max_freq = max_freq
    
    def forward(
        self, 
        tokens: torch.Tensor, 
        patch_positions: Optional[torch.Tensor] = None
    ) -> torch.Tensor:
        """
        生成傅里叶位置编码
        
        Args:
            tokens: [B, N, D] 
            patch_positions: [B, N, 2] 时-频域位置坐标 (可选)
            
        Returns:
            pos_encoding: [B, N, D]
        """
        B, N, D = tokens.shape
        
        if patch_positions is not None:
            # 使用真实的时-频域位置
            time_pos = patch_positions[:, :, 0]  # [B, N]
            freq_pos = patch_positions[:, :, 1]  # [B, N]
        else:
            # 使用线性位置索引
            positions = torch.arange(N, dtype=torch.float32, device=tokens.device)
            time_pos = positions.unsqueeze(0).expand(B, -1)  # [B, N]
            freq_pos = positions.unsqueeze(0).expand(B, -1)  # [B, N]
        
        # 生成频率
        freqs = torch.arange(0, D // 4, dtype=torch.float32, device=tokens.device)
        freqs = freqs / (D // 4)
        freqs = self.max_freq ** (-freqs)  # [D//4]
        
        # 计算时域和频域的编码
        time_enc = time_pos.unsqueeze(-1) * freqs.unsqueeze(0).unsqueeze(0)  # [B, N, D//4]
        freq_enc = freq_pos.unsqueeze(-1) * freqs.unsqueeze(0).unsqueeze(0)  # [B, N, D//4]
        
        # 组合时-频域编码
        pos_encoding = torch.zeros(B, N, D, device=tokens.device)
        pos_encoding[:, :, 0::4] = torch.sin(time_enc)
        pos_encoding[:, :, 1::4] = torch.cos(time_enc)
        pos_encoding[:, :, 2::4] = torch.sin(freq_enc)
        pos_encoding[:, :, 3::4] = torch.cos(freq_enc)
        
        return pos_encoding


class SpectralAudioTokenizer(AudioDARTTokenizer):
    """
    频谱增强音频Tokenizer
    
    专门针对音频频谱特性优化，包含频域特定的处理
    """
    
    def __init__(
        self,
        input_size: Tuple[int, int] = (64, 224),
        patch_size: int = 16,
        embed_dim: int = 768,
        spectral_norm: bool = True,
        freq_masking: bool = True,
        time_masking: bool = True,
        **kwargs
    ):
        super().__init__(
            input_size=input_size,
            patch_size=patch_size,
            embed_dim=embed_dim,
            **kwargs
        )
        
        self.spectral_norm = spectral_norm
        self.freq_masking = freq_masking
        self.time_masking = time_masking
        
        # 频谱归一化
        if spectral_norm:
            self.spec_norm = SpectralNormalizer()
        
        # 频谱数据增强
        if freq_masking or time_masking:
            self.spec_augment = SpectralAugmentation(
                freq_masking=freq_masking,
                time_masking=time_masking
            )
    
    def forward(
        self,
        audio_input: Union[torch.Tensor, Dict],
        num_patches: int,
        return_dict: bool = False
    ) -> Union[Tuple[torch.Tensor, torch.Tensor, Optional[torch.Tensor]], Dict]:
        """
        增强的前向传播，包含频谱预处理
        """
        # 获取谱图
        if isinstance(audio_input, dict):
            spec = audio_input.get('spectrogram', None)
            if spec is None:
                # 从波形生成
                waveform = audio_input.get('waveform', None)
                if waveform is not None:
                    spec = self.audio_preprocess.waveform_to_spectrogram(waveform)
                else:
                    raise ValueError("No valid audio input found")
        else:
            if audio_input.dim() == 2:
                spec = self.audio_preprocess.waveform_to_spectrogram(audio_input)
            else:
                spec = audio_input
        
        # 频谱归一化
        if self.spectral_norm:
            spec = self.spec_norm(spec)
        
        # 训练时应用数据增强
        if self.training and (self.freq_masking or self.time_masking):
            spec = self.spec_augment(spec)
        
        # 调用父类的tokenization
        return super().forward(spec, num_patches, return_dict)


class SpectralNormalizer(nn.Module):
    """频谱归一化器"""
    
    def forward(self, spectrogram: torch.Tensor) -> torch.Tensor:
        """
        归一化谱图到合理范围
        
        Args:
            spectrogram: [B, 1, T, F]
            
        Returns:
            normalized_spec: [B, 1, T, F]
        """
        # 沿频率维度归一化
        mean = spectrogram.mean(dim=-1, keepdim=True)
        std = spectrogram.std(dim=-1, keepdim=True) + 1e-8
        
        return (spectrogram - mean) / std


class SpectralAugmentation(nn.Module):
    """频谱数据增强"""
    
    def __init__(
        self,
        freq_masking: bool = True,
        time_masking: bool = True,
        freq_mask_ratio: float = 0.1,
        time_mask_ratio: float = 0.1
    ):
        super().__init__()
        
        self.freq_masking = freq_masking
        self.time_masking = time_masking
        self.freq_mask_ratio = freq_mask_ratio
        self.time_mask_ratio = time_mask_ratio
    
    def forward(self, spectrogram: torch.Tensor) -> torch.Tensor:
        """
        应用频谱增强
        
        Args:
            spectrogram: [B, 1, T, F]
            
        Returns:
            augmented_spec: [B, 1, T, F]
        """
        B, C, T, F = spectrogram.shape
        spec = spectrogram.clone()
        
        if self.freq_masking:
            # 频率掩码
            freq_mask_len = int(F * self.freq_mask_ratio)
            for b in range(B):
                f_start = torch.randint(0, F - freq_mask_len + 1, (1,)).item()
                spec[b, :, :, f_start:f_start + freq_mask_len] = 0
        
        if self.time_masking:
            # 时间掩码
            time_mask_len = int(T * self.time_mask_ratio)
            for b in range(B):
                t_start = torch.randint(0, T - time_mask_len + 1, (1,)).item()
                spec[b, :, t_start:t_start + time_mask_len, :] = 0
        
        return spec


# 便利函数  
def create_audio_tokenizer(
    input_size: Tuple[int, int] = (64, 224),
    embed_dim: int = 768,
    enable_energy_extraction: bool = True,
    spectral_mode: bool = False,
    **kwargs
) -> Union[AudioDARTTokenizer, SpectralAudioTokenizer]:
    """
    创建音频tokenizer的便利函数
    
    Args:
        input_size: 输入谱图尺寸 (T, F)
        embed_dim: 嵌入维度
        enable_energy_extraction: 是否启用能量提取
        spectral_mode: 是否使用频谱增强模式
        **kwargs: 其他参数
        
    Returns:
        音频tokenizer实例
    """
    if spectral_mode:
        return SpectralAudioTokenizer(
            input_size=input_size,
            embed_dim=embed_dim,
            enable_energy_extraction=enable_energy_extraction,
            **kwargs
        )
    else:
        return AudioDARTTokenizer(
            input_size=input_size,
            embed_dim=embed_dim,
            enable_energy_extraction=enable_energy_extraction,
            **kwargs
        )