"""
Enhanced AV-DART模型配置文件
使用全新升级的Enhanced AV-DART模型，集成所有优化组件
"""

encoder_channel_list = [768, 384, 192, 96]  # for swin
decoder_channel_list = encoder_channel_list

import yaml
from util.opts import dict2namespace
from DART.av_dart.models.standard_enhanced_av_dart_diffusion_wrapper import StandardEnhancedAVDARTDiffusionWrapper
from datasets.dhf1k_data import DHF1KDatasetMultiFrames
from datasets.holly2wood_dataset import HollyDataset
from datasets.ucf_dataset import UCFDataset

with open("cfgs/diffusion.yml", "r") as f:
    diff_config = yaml.safe_load(f)

new_config = dict2namespace(diff_config)

len_snippet = 32
data_type = "dhf1k"
gt_length = 1  # 8, 10, 12, 14, 16
img_size = (224, 384)

# Enhanced AV-DART模型配置 - 使用标准预训练编码器
config = dict(
    type=StandardEnhancedAVDARTDiffusionWrapper,
    embed_dim=96,
    total_tokens=196,
    num_transformer_layers=6,
    num_heads=8,
    visual_size=224,
    audio_size=(64, 224),
    output_size=img_size,
    token_strategy="energy_based",  # 智能动态分配
    
    # 标准预训练编码器配置
    freeze_pretrained_encoders=True,  # DHF1K预训练阶段冻结编码器
    visual_encoder_config=dict(
        arch='small',
        pretrained_path='data/pretrained_models/mvit-small-p244_32xb16-16x4x1-200e_kinetics400-rgb_20230201-23284ff3.pth',
        out_scales=[0, 1, 2, 3],
        freeze_backbone=True
    ),
    audio_encoder_config=dict(
        pretrained=True,
        freeze_backbone=True
    ),
    
    # 升级功能开关 - 全部启用
    use_pyramid_dart=True,        # 多尺度视觉分词
    use_rope=True,                # RoPE位置编码
    use_gated_fusion=True,        # 门控融合
    use_center_bias=True,         # 中心偏置先验
    use_multiscale_supervision=True,  # 多尺度监督
    use_multi_metric_loss=True,   # 多指标损失
    
    # 融合配置
    num_fusion_layers=3,          # 3层AV融合块
    use_layer_scale=False,        # 层级缩放（可选）
    
    # RoPE配置
    rope_theta=10000.0,           # RoPE频率参数
    dropout=0.1,                  # Dropout率
    
    # 解码器配置
    decoder_hidden_dim=256,       # 解码器隐藏维度
    gate_type="se",               # 门控类型：se/cbam/simple
    center_bias_type="learnable", # 中心偏置类型
    
    # 损失函数权重
    kl_weight=1.0,                # KL散度权重
    cc_weight=0.7,                # 相关系数权重
    sim_weight=0.5,               # 相似性权重
    nss_weight=1.0,               # NSS权重
    
    # 动态token分配策略参数
    visual_ratio=0.6,             # 默认视觉token比例
    min_visual_ratio=0.3,         # 最小视觉token比例
    max_visual_ratio=0.8,         # 最大视觉token比例
    energy_weight=0.4             # 能量权重
)

data_dict = {
    "dhf1k": {
        "type": DHF1KDatasetMultiFrames,
        "path": "VideoSalPrediction/DHF1k_extracted",
    },
    "holly": {
        "type": HollyDataset,
        "path": "VideoSalPrediction/Hollywood2",
    },
    "ucf": {
        "type": UCFDataset,
        "path": "VideoSalPrediction/ucf",
    },
}

data = dict(
    train=dict(
        type=data_dict[data_type]["type"],
        path_data=data_dict[data_type]["path"],
        len_snippet=len_snippet,
        mode="train",
        img_size=img_size,
        alternate=1,
        gt_length=gt_length,
    ),
    val=dict(
        type=data_dict[data_type]["type"],
        path_data=data_dict[data_type]["path"],
        len_snippet=len_snippet,
        mode="val",
        img_size=img_size,
        alternate=1,
        gt_length=gt_length,
    ),
)