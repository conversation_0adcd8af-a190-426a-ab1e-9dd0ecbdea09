#!/usr/bin/env python3
"""
AV-DART 完整训练器
基于DiffusionTrainer，但针对AV-DART模型进行优化
包含完整的train、val、test功能
"""

import os
import time
import cv2
import json
import numpy as np
import copy
import torch
import torch.nn as nn
from torch import optim
from torch.utils.data import DataLoader
from tqdm import tqdm
import sys

# 添加路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(current_dir)
sys.path.insert(0, project_root)

# 导入AV-DART模型
from DART.av_dart.models.av_dart_model import AVDARTModel

# 导入原始项目组件
try:
    from models.sal_losses import get_kl_cc_sim_loss_wo_weight, get_lossv2
    from util.utils import (
        get_optim_scheduler,
        AverageMeter,
        Logger,
        normalize_data,
        LogWritter,
        AverageMeterList,
    )
    from datasets.prepare_data import (
        get_training_av_loader,
        get_val_av_loader,
        get_test_av_loader,
    )
    from datasets import inverse_data_transform
except ImportError as e:
    print(f"警告: 无法导入某些组件: {e}")


class AVDARTTrainer(object):
    """AV-DART完整训练器，包含train、val、test功能"""
    
    def __init__(self, args, config, device=None):
        self.args = args
        self.config = config
        
        if device is None:
            device = (
                torch.device("cuda")
                if torch.cuda.is_available()
                else torch.device("cpu")
            )
        self.device = device
        
        # 创建AV-DART模型
        self.model = self._create_av_dart_model()
        self.model = self.model.to(device)
        
        # 多GPU支持
        if not args.multiprocessing_distributed:
            self.model = nn.DataParallel(self.model.cuda())
        else:
            torch.cuda.set_device(args.gpu)
            self.model.cuda(args.gpu)
            self.model = torch.nn.parallel.DistributedDataParallel(
                self.model, device_ids=[args.gpu], find_unused_parameters=True
            )
        
        print("AV-DART模型创建完成:")
        print(self.model)
    
    def _create_av_dart_model(self):
        """创建AV-DART模型"""
        # 从配置文件获取模型参数
        try:
            # 尝试从配置文件获取参数
            model_config = getattr(self.config, 'model', {})
            embed_dim = getattr(model_config, 'embed_dim', 768)
            total_tokens = getattr(model_config, 'total_tokens', 196)
            num_layers = getattr(model_config, 'num_layers', 6)
            num_heads = getattr(model_config, 'num_heads', 8)
        except:
            # 使用默认参数
            embed_dim = 768
            total_tokens = 196
            num_layers = 6
            num_heads = 8
        
        model = AVDARTModel(
            embed_dim=embed_dim,
            total_tokens=total_tokens,
            num_layers=num_layers,
            num_heads=num_heads,
            output_size=(224, 384),
            token_strategy="dynamic"
        )
        
        return model
    
    def prepare_data(self, data, targets, is_training=True):
        """准备训练数据，简化版本，专注于AV-DART"""
        # 处理显著性图
        if type(targets["salmap"]) == dict:
            targets["salmap"] = {
                key: targets["salmap"][key].cuda() for key in targets["salmap"]
            }
        else:
            targets["salmap"] = targets["salmap"].cuda()
            targets["salmap"] = targets["salmap"].float()

        sal_maps = targets["salmap"]
        if len(sal_maps.shape) == 5:
            sal_maps = sal_maps.view(
                -1, sal_maps.shape[1], sal_maps.shape[3], sal_maps.shape[4]
            )

        # 处理图像数据
        while data["rgb"].size(0) < self.args.batch_size:
            tmp_size = self.args.batch_size - data["rgb"].size(0)
            img_list = []
            for _ in range(tmp_size):
                img_list.append(data["rgb"][-1, :])
            img_tensor = torch.stack(img_list, 0)
            data["rgb"] = torch.cat((data["rgb"], img_tensor), 0)

        imgs = (
            data["rgb"]
            .view(-1, data["rgb"].shape[1], data["rgb"].shape[3], data["rgb"].shape[4])
            .cuda()
        )
        
        # 处理音频数据
        audio = data["audio"].cuda()
        
        return imgs, audio, sal_maps
    
    def train_av_data(self):
        """训练音视频数据集 - 完整的训练流程"""
        args, config = self.args, self.config
        json_path = "cfgs/dataset.json"
        with open(json_path) as fp:
            data_config = json.load(fp)

        split_list = ["split1", "split2", "split3"]
        for split in split_list:
            result_path = "{}_results".format(split)
            weight_path = "{}_weights".format(split)
            args.result_path = os.path.join(args.root_path, result_path)
            args.weight_path = os.path.join(args.root_path, weight_path)

            os.makedirs(args.result_path, exist_ok=True)
            os.makedirs(args.weight_path, exist_ok=True)

            print("保存路径: {}".format(args.result_path))
            print("权重路径: {}".format(args.weight_path))
            data_config["index"] = split

            # 获取训练轮数
            num_epoches = getattr(config.training, 'n_epochs_for_av_data', 10)
            train_loader = get_training_av_loader(args, data_config)
            
            # 创建优化器
            optimizer = optim.AdamW(self.model.parameters(), lr=args.lr, weight_decay=0.01)
            scheduler = optim.lr_scheduler.MultiStepLR(optimizer, milestones=[5, 8], gamma=0.1)

            # 日志记录
            train_model_log = LogWritter(
                os.path.join(args.result_path, "training_model.txt")
            )
            if self.args.rank == 0:
                train_model_log.update_txt(self.model.module, mode="w")

            train_logger = Logger(
                os.path.join(args.result_path, "train.log"),
                ["epoch", "total_step", "loss", "main", "lr"],
            )
            val_logger = Logger(
                os.path.join(args.result_path, "val.log"),
                ["epoch", "total_step", "loss", "main"],
            )

            start_epoch, step = 0, 0
            if hasattr(args, 'resume_training') and args.resume_training:
                if os.path.exists(args.pretrain_path):
                    states = torch.load(
                        args.pretrain_path, map_location=torch.device("cpu")
                    )
                    msg = self.model.load_state_dict(states["state_dict"], strict=False)
                    if 'optim_dict' in states:
                        optimizer.load_state_dict(states["optim_dict"])
                    start_epoch = states.get("epoch", 0)
                    step = states.get("step", 0)
                    print("恢复训练: {}/{}".format(args.pretrain_path, msg))

            # 开始训练
            best_loss = float('inf')
            for epoch in range(start_epoch, num_epoches):
                train_time = AverageMeter()
                data_time = AverageMeter()
                loss_meter = AverageMeter()

                self.model.train()
                data_start = time.time()
                
                train_progress = tqdm(train_loader, desc=f"训练 Epoch {epoch+1}/{num_epoches}", leave=True)
                
                for i, (data, targets) in enumerate(train_progress):
                    imgs, audio, sal_maps = self.prepare_data(data, targets)
                    data_time.update(time.time() - data_start)

                    step += 1

                    # 前向传播
                    pred_sal = self.model(imgs, audio)
                    
                    # 计算损失 - 使用简单的MSE损失
                    loss = nn.functional.mse_loss(pred_sal, sal_maps)
                    
                    loss_meter.update(loss.item())
                    
                    # 反向传播
                    optimizer.zero_grad()
                    loss.backward()
                    
                    # 梯度裁剪
                    torch.nn.utils.clip_grad_norm_(self.model.parameters(), 1.0)
                    
                    optimizer.step()

                    train_time.update(time.time() - data_start)
                    
                    # 更新进度条
                    train_progress.set_postfix({
                        'loss': f"{loss.item():.4f}",
                        'avg_loss': f"{loss_meter.avg:.4f}",
                        'lr': f"{optimizer.param_groups[0]['lr']:.2e}"
                    })
                    
                    data_start = time.time()

                # 验证
                val_loss = self.test_av_data_for_training(
                    data_config,
                    epoch=epoch + 1,
                    step=step,
                    val_logger=val_logger,
                    save_img=False,
                )
                
                # 保存检查点
                states = {
                    "state_dict": self.model.state_dict(),
                    "optim_dict": optimizer.state_dict(),
                    "epoch": epoch + 1,
                    "step": step,
                }
                torch.save(
                    states,
                    os.path.join(
                        self.args.weight_path, "ckpt_{}.pth".format(epoch + 1)
                    ),
                )

                # 保存最佳模型
                if val_loss < best_loss:
                    best_loss = val_loss
                    torch.save(states, os.path.join(self.args.weight_path, "best.pth"))
                    print(f"保存最佳模型 (验证损失: {val_loss:.4f})")

                # 记录训练日志
                if self.args.rank == 0:
                    train_logger.log({
                        "epoch": epoch + 1,
                        "total_step": step,
                        "loss": round(loss_meter.avg, 4),
                        "main": round(loss_meter.avg, 4),
                        "lr": optimizer.param_groups[0]["lr"],
                    })
                
                scheduler.step()
                
                print(f"Epoch {epoch+1}/{num_epoches} - 训练损失: {loss_meter.avg:.4f}, 验证损失: {val_loss:.4f}")

            print("训练完成!")

    @torch.no_grad()
    def test_av_data_for_training(
        self,
        data_config,
        epoch=0,
        step=0,
        val_logger=None,
        save_img=False,
    ):
        """训练过程中的验证"""
        print("开始验证...")
        args = self.args

        split = data_config["index"]
        result_path = f"{split}_results"
        result_path = os.path.join(args.root_path, result_path)
        os.makedirs(result_path, exist_ok=True)

        loss_meter = AverageMeter()

        val_loader = get_val_av_loader(args, data_config)
        
        self.model.eval()
        val_progress = tqdm(val_loader, desc=f"验证 Epoch {epoch}", leave=True)
        
        for i, (data, targets) in enumerate(val_progress):
            imgs, audio, sal_maps = self.prepare_data(data, targets, is_training=False)
            
            # 前向传播
            pred_sal = self.model(imgs, audio)
            
            # 计算损失
            loss = nn.functional.mse_loss(pred_sal, sal_maps)
            loss_meter.update(loss.item())
            
            # 更新进度条
            val_progress.set_postfix({'val_loss': f"{loss.item():.4f}"})

            # 保存预测图像（可选）
            if save_img:
                self.save_img(data, pred_sal, result_path, av_data=True)

        # 记录验证日志
        if val_logger is not None and args.rank == 0:
            val_logger.log({
                "epoch": epoch,
                "total_step": step,
                "loss": round(loss_meter.avg, 4),
                "main": round(loss_meter.avg, 4),
            })

        return loss_meter.avg

    @torch.no_grad()
    def test_av_data(self, epoch=0, save_img=True):
        """测试音视频数据集"""
        print("开始测试音视频数据集...")
        args = self.args

        json_path = "cfgs/dataset.json"
        with open(json_path) as fp:
            data_config = json.load(fp)

        split_list = ["split1", "split2", "split3"]
        for split in split_list:
            data_config["index"] = split
            weight_path = "{}_weights".format(split)
            weight_path = os.path.join(args.root_path, weight_path)

            result_path = f"{split}_results"
            result_path = os.path.join(args.root_path, result_path)
            os.makedirs(result_path, exist_ok=True)

            test_logger = Logger(
                os.path.join(result_path, "test.log"),
                ["epoch", "total_step", "loss", "main"],
            )

            # 加载最佳模型
            args.pretrain_path = os.path.join(weight_path, "best.pth")
            if args.pretrain_path.strip() != "" and os.path.exists(args.pretrain_path):
                states = torch.load(
                    args.pretrain_path, map_location=torch.device("cpu")
                )
                msg = self.model.load_state_dict(states["state_dict"], strict=False)
                print("测试加载模型: {}/{}".format(args.pretrain_path, msg))

            loss_meter = AverageMeter()
            test_loader = get_test_av_loader(args, data_config)
            
            self.model.eval()
            test_progress = tqdm(test_loader, desc=f"测试 {split}", leave=True)

            for i, (data, targets) in enumerate(test_progress):
                imgs, audio, sal_maps = self.prepare_data(data, targets, is_training=False)
                
                # 前向传播
                pred_sal = self.model(imgs, audio)
                
                # 计算损失
                loss = nn.functional.mse_loss(pred_sal, sal_maps)
                loss_meter.update(loss.item())
                
                # 更新进度条
                test_progress.set_postfix({'test_loss': f"{loss.item():.4f}"})

                # 保存预测图像
                if save_img:
                    self.save_img(data, pred_sal, result_path, av_data=True)

            # 记录测试日志
            if test_logger is not None and args.rank == 0:
                test_logger.log({
                    "epoch": epoch,
                    "total_step": 0,
                    "loss": round(loss_meter.avg, 4),
                    "main": round(loss_meter.avg, 4),
                })
            
            print(f"{split} 测试完成 - 平均损失: {loss_meter.avg:.4f}")

    def save_img(self, data, pred, save_root, av_data=False):
        """保存预测图像"""
        video_ids = data["video_index"]
        gt_indexes = data["gt_index"].cpu().numpy()
        pred = pred.detach().cpu().numpy()
        gt_indexes = gt_indexes.reshape(pred.shape[0], 1)

        if av_data:
            data_convert_dict = {
                "AVAD": "avad",
                "Coutrot_db1": "coutrot1",
                "Coutrot_db2": "coutrot2",
                "DIEM": "diem",
                "ETMD_av": "etmd",
                "SumMe": "summe",
            }

        assert pred.shape[0] == len(video_ids) == len(gt_indexes)
        for i, (img, vid, gid) in enumerate(zip(pred, video_ids, gt_indexes)):
            if av_data:
                vid = str(
                    data_convert_dict.get(vid.split("/")[0], vid.split("/")[0]) + "/" + vid.split("/")[-1]
                )
                img_name = "pred_sal_{0:06d}.jpg".format(int(gid))
            else:
                vid = str(vid)
                img_name = str(int(gid)) + ".png"

            save_path = os.path.join(save_root, vid)
            os.makedirs(save_path, exist_ok=True)
            sal_img = normalize_data(img.transpose(1, 2, 0))
            cv2.imwrite("{}/{}".format(save_path, img_name), sal_img)