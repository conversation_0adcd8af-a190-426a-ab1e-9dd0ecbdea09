VideoSaliencyModel(
  (visual_net): MViT(
    (patch_embed): PatchEmbed3D(
      (projection): Conv3d(3, 96, kernel_size=(3, 7, 7), stride=(2, 4, 4), padding=(1, 3, 3))
    )
    (blocks): ModuleList(
      (0): MultiScaleBlock(
        (norm1): LayerNorm((96,), eps=1e-05, elementwise_affine=True)
        (attn): MultiScaleAttention(
          (qkv): Linear(in_features=96, out_features=288, bias=True)
          (proj): Linear(in_features=96, out_features=96, bias=True)
          (pool_q): Conv3d(96, 96, kernel_size=(3, 3, 3), stride=(1, 1, 1), padding=(1, 1, 1), groups=96, bias=False)
          (norm_q): LayerNorm((96,), eps=1e-05, elementwise_affine=True)
          (pool_k): Conv3d(96, 96, kernel_size=(3, 3, 3), stride=(1, 8, 8), padding=(1, 1, 1), groups=96, bias=False)
          (norm_k): LayerNorm((96,), eps=1e-05, elementwise_affine=True)
          (pool_v): Conv3d(96, 96, kernel_size=(3, 3, 3), stride=(1, 8, 8), padding=(1, 1, 1), groups=96, bias=False)
          (norm_v): LayerNorm((96,), eps=1e-05, elementwise_affine=True)
        )
        (drop_path): Identity()
        (norm2): LayerNorm((96,), eps=1e-05, elementwise_affine=True)
        (mlp): MLP(
          (fc1): Linear(in_features=96, out_features=384, bias=True)
          (act): GELU(approximate='none')
          (fc2): Linear(in_features=384, out_features=96, bias=True)
        )
      )
      (1): MultiScaleBlock(
        (norm1): LayerNorm((96,), eps=1e-05, elementwise_affine=True)
        (attn): MultiScaleAttention(
          (qkv): Linear(in_features=96, out_features=576, bias=True)
          (proj): Linear(in_features=192, out_features=192, bias=True)
          (pool_q): Conv3d(96, 96, kernel_size=(3, 3, 3), stride=(1, 2, 2), padding=(1, 1, 1), groups=96, bias=False)
          (norm_q): LayerNorm((96,), eps=1e-05, elementwise_affine=True)
          (pool_k): Conv3d(96, 96, kernel_size=(3, 3, 3), stride=(1, 4, 4), padding=(1, 1, 1), groups=96, bias=False)
          (norm_k): LayerNorm((96,), eps=1e-05, elementwise_affine=True)
          (pool_v): Conv3d(96, 96, kernel_size=(3, 3, 3), stride=(1, 4, 4), padding=(1, 1, 1), groups=96, bias=False)
          (norm_v): LayerNorm((96,), eps=1e-05, elementwise_affine=True)
        )
        (drop_path): Identity()
        (norm2): LayerNorm((192,), eps=1e-05, elementwise_affine=True)
        (mlp): MLP(
          (fc1): Linear(in_features=192, out_features=768, bias=True)
          (act): GELU(approximate='none')
          (fc2): Linear(in_features=768, out_features=192, bias=True)
        )
        (proj): Linear(in_features=96, out_features=192, bias=True)
        (pool_skip): MaxPool3d(kernel_size=[1, 3, 3], stride=[1, 2, 2], padding=[0, 1, 1], dilation=1, ceil_mode=False)
      )
      (2): MultiScaleBlock(
        (norm1): LayerNorm((192,), eps=1e-05, elementwise_affine=True)
        (attn): MultiScaleAttention(
          (qkv): Linear(in_features=192, out_features=576, bias=True)
          (proj): Linear(in_features=192, out_features=192, bias=True)
          (pool_q): Conv3d(96, 96, kernel_size=(3, 3, 3), stride=(1, 1, 1), padding=(1, 1, 1), groups=96, bias=False)
          (norm_q): LayerNorm((96,), eps=1e-05, elementwise_affine=True)
          (pool_k): Conv3d(96, 96, kernel_size=(3, 3, 3), stride=(1, 4, 4), padding=(1, 1, 1), groups=96, bias=False)
          (norm_k): LayerNorm((96,), eps=1e-05, elementwise_affine=True)
          (pool_v): Conv3d(96, 96, kernel_size=(3, 3, 3), stride=(1, 4, 4), padding=(1, 1, 1), groups=96, bias=False)
          (norm_v): LayerNorm((96,), eps=1e-05, elementwise_affine=True)
        )
        (drop_path): Identity()
        (norm2): LayerNorm((192,), eps=1e-05, elementwise_affine=True)
        (mlp): MLP(
          (fc1): Linear(in_features=192, out_features=768, bias=True)
          (act): GELU(approximate='none')
          (fc2): Linear(in_features=768, out_features=192, bias=True)
        )
      )
      (3): MultiScaleBlock(
        (norm1): LayerNorm((192,), eps=1e-05, elementwise_affine=True)
        (attn): MultiScaleAttention(
          (qkv): Linear(in_features=192, out_features=1152, bias=True)
          (proj): Linear(in_features=384, out_features=384, bias=True)
          (pool_q): Conv3d(96, 96, kernel_size=(3, 3, 3), stride=(1, 2, 2), padding=(1, 1, 1), groups=96, bias=False)
          (norm_q): LayerNorm((96,), eps=1e-05, elementwise_affine=True)
          (pool_k): Conv3d(96, 96, kernel_size=(3, 3, 3), stride=(1, 2, 2), padding=(1, 1, 1), groups=96, bias=False)
          (norm_k): LayerNorm((96,), eps=1e-05, elementwise_affine=True)
          (pool_v): Conv3d(96, 96, kernel_size=(3, 3, 3), stride=(1, 2, 2), padding=(1, 1, 1), groups=96, bias=False)
          (norm_v): LayerNorm((96,), eps=1e-05, elementwise_affine=True)
        )
        (drop_path): Identity()
        (norm2): LayerNorm((384,), eps=1e-05, elementwise_affine=True)
        (mlp): MLP(
          (fc1): Linear(in_features=384, out_features=1536, bias=True)
          (act): GELU(approximate='none')
          (fc2): Linear(in_features=1536, out_features=384, bias=True)
        )
        (proj): Linear(in_features=192, out_features=384, bias=True)
        (pool_skip): MaxPool3d(kernel_size=[1, 3, 3], stride=[1, 2, 2], padding=[0, 1, 1], dilation=1, ceil_mode=False)
      )
      (4-13): 10 x MultiScaleBlock(
        (norm1): LayerNorm((384,), eps=1e-05, elementwise_affine=True)
        (attn): MultiScaleAttention(
          (qkv): Linear(in_features=384, out_features=1152, bias=True)
          (proj): Linear(in_features=384, out_features=384, bias=True)
          (pool_q): Conv3d(96, 96, kernel_size=(3, 3, 3), stride=(1, 1, 1), padding=(1, 1, 1), groups=96, bias=False)
          (norm_q): LayerNorm((96,), eps=1e-05, elementwise_affine=True)
          (pool_k): Conv3d(96, 96, kernel_size=(3, 3, 3), stride=(1, 2, 2), padding=(1, 1, 1), groups=96, bias=False)
          (norm_k): LayerNorm((96,), eps=1e-05, elementwise_affine=True)
          (pool_v): Conv3d(96, 96, kernel_size=(3, 3, 3), stride=(1, 2, 2), padding=(1, 1, 1), groups=96, bias=False)
          (norm_v): LayerNorm((96,), eps=1e-05, elementwise_affine=True)
        )
        (drop_path): Identity()
        (norm2): LayerNorm((384,), eps=1e-05, elementwise_affine=True)
        (mlp): MLP(
          (fc1): Linear(in_features=384, out_features=1536, bias=True)
          (act): GELU(approximate='none')
          (fc2): Linear(in_features=1536, out_features=384, bias=True)
        )
      )
      (14): MultiScaleBlock(
        (norm1): LayerNorm((384,), eps=1e-05, elementwise_affine=True)
        (attn): MultiScaleAttention(
          (qkv): Linear(in_features=384, out_features=2304, bias=True)
          (proj): Linear(in_features=768, out_features=768, bias=True)
          (pool_q): Conv3d(96, 96, kernel_size=(3, 3, 3), stride=(1, 2, 2), padding=(1, 1, 1), groups=96, bias=False)
          (norm_q): LayerNorm((96,), eps=1e-05, elementwise_affine=True)
          (pool_k): Conv3d(96, 96, kernel_size=(3, 3, 3), stride=(1, 1, 1), padding=(1, 1, 1), groups=96, bias=False)
          (norm_k): LayerNorm((96,), eps=1e-05, elementwise_affine=True)
          (pool_v): Conv3d(96, 96, kernel_size=(3, 3, 3), stride=(1, 1, 1), padding=(1, 1, 1), groups=96, bias=False)
          (norm_v): LayerNorm((96,), eps=1e-05, elementwise_affine=True)
        )
        (drop_path): Identity()
        (norm2): LayerNorm((768,), eps=1e-05, elementwise_affine=True)
        (mlp): MLP(
          (fc1): Linear(in_features=768, out_features=3072, bias=True)
          (act): GELU(approximate='none')
          (fc2): Linear(in_features=3072, out_features=768, bias=True)
        )
        (proj): Linear(in_features=384, out_features=768, bias=True)
        (pool_skip): MaxPool3d(kernel_size=[1, 3, 3], stride=[1, 2, 2], padding=[0, 1, 1], dilation=1, ceil_mode=False)
      )
      (15): MultiScaleBlock(
        (norm1): LayerNorm((768,), eps=1e-05, elementwise_affine=True)
        (attn): MultiScaleAttention(
          (qkv): Linear(in_features=768, out_features=2304, bias=True)
          (proj): Linear(in_features=768, out_features=768, bias=True)
          (pool_q): Conv3d(96, 96, kernel_size=(3, 3, 3), stride=(1, 1, 1), padding=(1, 1, 1), groups=96, bias=False)
          (norm_q): LayerNorm((96,), eps=1e-05, elementwise_affine=True)
          (pool_k): Conv3d(96, 96, kernel_size=(3, 3, 3), stride=(1, 1, 1), padding=(1, 1, 1), groups=96, bias=False)
          (norm_k): LayerNorm((96,), eps=1e-05, elementwise_affine=True)
          (pool_v): Conv3d(96, 96, kernel_size=(3, 3, 3), stride=(1, 1, 1), padding=(1, 1, 1), groups=96, bias=False)
          (norm_v): LayerNorm((96,), eps=1e-05, elementwise_affine=True)
        )
        (drop_path): Identity()
        (norm2): LayerNorm((768,), eps=1e-05, elementwise_affine=True)
        (mlp): MLP(
          (fc1): Linear(in_features=768, out_features=3072, bias=True)
          (act): GELU(approximate='none')
          (fc2): Linear(in_features=3072, out_features=768, bias=True)
        )
      )
    )
    (norm0): LayerNorm((96,), eps=1e-05, elementwise_affine=True)
    (norm1): LayerNorm((192,), eps=1e-05, elementwise_affine=True)
    (norm2): LayerNorm((384,), eps=1e-05, elementwise_affine=True)
    (norm3): LayerNorm((768,), eps=1e-05, elementwise_affine=True)
  )
  (decoder_net): SalUNet(
    (invpt_decoder): Decoder(
      (norm_mts): ModuleList(
        (0): LayerNorm((768,), eps=1e-05, elementwise_affine=True)
        (1): LayerNorm((384,), eps=1e-05, elementwise_affine=True)
        (2): LayerNorm((192,), eps=1e-05, elementwise_affine=True)
        (3): LayerNorm((96,), eps=1e-05, elementwise_affine=True)
      )
      (redu_chan_up): ModuleList(
        (0): ReduceTemp(
          (proj): Sequential(
            (0): Conv3d(768, 768, kernel_size=(5, 1, 1), stride=(5, 1, 1), bias=False)
            (1): ReLU(inplace=True)
          )
        )
        (1): ReduceTemp(
          (proj): Sequential(
            (0): Conv3d(384, 768, kernel_size=(5, 1, 1), stride=(5, 1, 1), bias=False)
            (1): ReLU(inplace=True)
          )
        )
        (2): ReduceTemp(
          (proj): Sequential(
            (0): Conv3d(192, 768, kernel_size=(5, 1, 1), stride=(5, 1, 1), bias=False)
            (1): ReLU(inplace=True)
          )
        )
        (3): ReduceTemp(
          (proj): Sequential(
            (0): Conv3d(96, 768, kernel_size=(5, 1, 1), stride=(5, 1, 1), bias=False)
            (1): ReLU(inplace=True)
          )
        )
      )
      (mid_stages): ModuleList(
        (0): TransformerStage(
          (blocks): ModuleList(
            (0): TransformerBlock(
              (drop_path): Identity()
              (mlp): Mlp(
                (fc1): Linear(in_features=768, out_features=1536, bias=True)
                (act): GELU(approximate='none')
                (fc2): Linear(in_features=1536, out_features=768, bias=True)
                (drop): Dropout(p=0, inplace=False)
              )
              (norm): LayerNorm((768,), eps=1e-05, elementwise_affine=True)
              (attn): Attention(
                (conv_proj_q): Sequential(
                  (conv): Conv3d(768, 768, kernel_size=(3, 3, 3), stride=(1, 1, 1), padding=(1, 1, 1), groups=768, bias=False)
                  (rearrage): Rearrange('b c t h w -> b (t h w) c')
                  (bn): LayerNorm((768,), eps=1e-05, elementwise_affine=True)
                )
                (conv_proj_k): Sequential(
                  (conv): Conv3d(768, 768, kernel_size=(1, 2, 2), stride=(1, 2, 2), groups=768, bias=False)
                  (rearrage): Rearrange('b c t h w -> b (t h w) c')
                  (bn): LayerNorm((768,), eps=1e-05, elementwise_affine=True)
                )
                (conv_proj_v): Sequential(
                  (conv): Conv3d(768, 768, kernel_size=(1, 2, 2), stride=(1, 2, 2), groups=768, bias=False)
                  (rearrage): Rearrange('b c t h w -> b (t h w) c')
                  (bn): LayerNorm((768,), eps=1e-05, elementwise_affine=True)
                )
                (proj_q): Linear(in_features=768, out_features=768, bias=True)
                (proj_k): Linear(in_features=768, out_features=768, bias=True)
                (proj_v): Linear(in_features=768, out_features=768, bias=True)
                (attn_drop): Dropout(p=0, inplace=False)
                (proj): Linear(in_features=768, out_features=768, bias=True)
                (proj_drop): Dropout(p=0, inplace=False)
              )
              (norm2): LayerNorm((768,), eps=1e-05, elementwise_affine=True)
              (align_conv): Conv2d(512, 768, kernel_size=(1, 1), stride=(1, 1))
            )
          )
        )
        (1): TransformerStage(
          (patch_embed): ModuleList(
            (0): UpEmbed(
              (proj): Sequential(
                (0): Upsample(scale_factor=2.0, mode='bilinear')
                (1): Conv2d(768, 384, kernel_size=(3, 3), stride=(1, 1), padding=(2, 2), dilation=(2, 2), bias=False)
                (2): BatchNorm2d(384, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
                (3): ReLU(inplace=True)
                (4): Conv2d(384, 384, kernel_size=(3, 3), stride=(1, 1), padding=(2, 2), dilation=(2, 2), bias=False)
                (5): BatchNorm2d(384, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
                (6): ReLU(inplace=True)
              )
            )
          )
          (blocks): ModuleList(
            (0): TransformerBlock(
              (drop_path): Identity()
              (mlp): Mlp(
                (fc1): Linear(in_features=384, out_features=768, bias=True)
                (act): GELU(approximate='none')
                (fc2): Linear(in_features=768, out_features=384, bias=True)
                (drop): Dropout(p=0, inplace=False)
              )
              (norm): LayerNorm((384,), eps=1e-05, elementwise_affine=True)
              (attn): Attention(
                (conv_proj_q): Sequential(
                  (conv): Conv3d(384, 384, kernel_size=(3, 3, 3), stride=(1, 1, 1), padding=(1, 1, 1), groups=384, bias=False)
                  (rearrage): Rearrange('b c t h w -> b (t h w) c')
                  (bn): LayerNorm((384,), eps=1e-05, elementwise_affine=True)
                )
                (conv_proj_k): Sequential(
                  (conv): Conv3d(384, 384, kernel_size=(1, 4, 4), stride=(1, 4, 4), groups=384, bias=False)
                  (rearrage): Rearrange('b c t h w -> b (t h w) c')
                  (bn): LayerNorm((384,), eps=1e-05, elementwise_affine=True)
                )
                (conv_proj_v): Sequential(
                  (conv): Conv3d(384, 384, kernel_size=(1, 4, 4), stride=(1, 4, 4), groups=384, bias=False)
                  (rearrage): Rearrange('b c t h w -> b (t h w) c')
                  (bn): LayerNorm((384,), eps=1e-05, elementwise_affine=True)
                )
                (proj_q): Linear(in_features=384, out_features=384, bias=True)
                (proj_k): Linear(in_features=384, out_features=384, bias=True)
                (proj_v): Linear(in_features=384, out_features=384, bias=True)
                (attn_drop): Dropout(p=0, inplace=False)
                (proj): Linear(in_features=384, out_features=384, bias=True)
                (proj_drop): Dropout(p=0, inplace=False)
              )
              (norm2): LayerNorm((384,), eps=1e-05, elementwise_affine=True)
              (align_conv): Conv2d(512, 384, kernel_size=(1, 1), stride=(1, 1))
            )
          )
        )
        (2): TransformerStage(
          (patch_embed): ModuleList(
            (0): UpEmbed(
              (proj): Sequential(
                (0): Upsample(scale_factor=2.0, mode='bilinear')
                (1): Conv2d(384, 192, kernel_size=(3, 3), stride=(1, 1), padding=(2, 2), dilation=(2, 2), bias=False)
                (2): BatchNorm2d(192, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
                (3): ReLU(inplace=True)
                (4): Conv2d(192, 192, kernel_size=(3, 3), stride=(1, 1), padding=(2, 2), dilation=(2, 2), bias=False)
                (5): BatchNorm2d(192, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
                (6): ReLU(inplace=True)
              )
            )
          )
          (blocks): ModuleList(
            (0): TransformerBlock(
              (drop_path): Identity()
              (mlp): Mlp(
                (fc1): Linear(in_features=192, out_features=384, bias=True)
                (act): GELU(approximate='none')
                (fc2): Linear(in_features=384, out_features=192, bias=True)
                (drop): Dropout(p=0, inplace=False)
              )
              (norm): LayerNorm((192,), eps=1e-05, elementwise_affine=True)
              (attn): Attention(
                (conv_proj_q): Sequential(
                  (conv): Conv3d(192, 192, kernel_size=(3, 3, 3), stride=(1, 1, 1), padding=(1, 1, 1), groups=192, bias=False)
                  (rearrage): Rearrange('b c t h w -> b (t h w) c')
                  (bn): LayerNorm((192,), eps=1e-05, elementwise_affine=True)
                )
                (conv_proj_k): Sequential(
                  (conv): Conv3d(192, 192, kernel_size=(1, 8, 8), stride=(1, 8, 8), groups=192, bias=False)
                  (rearrage): Rearrange('b c t h w -> b (t h w) c')
                  (bn): LayerNorm((192,), eps=1e-05, elementwise_affine=True)
                )
                (conv_proj_v): Sequential(
                  (conv): Conv3d(192, 192, kernel_size=(1, 8, 8), stride=(1, 8, 8), groups=192, bias=False)
                  (rearrage): Rearrange('b c t h w -> b (t h w) c')
                  (bn): LayerNorm((192,), eps=1e-05, elementwise_affine=True)
                )
                (proj_q): Linear(in_features=192, out_features=192, bias=True)
                (proj_k): Linear(in_features=192, out_features=192, bias=True)
                (proj_v): Linear(in_features=192, out_features=192, bias=True)
                (attn_drop): Dropout(p=0, inplace=False)
                (proj): Linear(in_features=192, out_features=192, bias=True)
                (proj_drop): Dropout(p=0, inplace=False)
              )
              (norm2): LayerNorm((192,), eps=1e-05, elementwise_affine=True)
              (align_conv): Conv2d(512, 192, kernel_size=(1, 1), stride=(1, 1))
            )
          )
        )
        (3): TransformerStage(
          (patch_embed): ModuleList(
            (0): UpEmbed(
              (proj): Sequential(
                (0): Upsample(scale_factor=2.0, mode='bilinear')
                (1): Conv2d(192, 96, kernel_size=(3, 3), stride=(1, 1), padding=(2, 2), dilation=(2, 2), bias=False)
                (2): BatchNorm2d(96, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
                (3): ReLU(inplace=True)
                (4): Conv2d(96, 96, kernel_size=(3, 3), stride=(1, 1), padding=(2, 2), dilation=(2, 2), bias=False)
                (5): BatchNorm2d(96, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
                (6): ReLU(inplace=True)
              )
            )
          )
          (blocks): ModuleList(
            (0): TransformerBlock(
              (drop_path): Identity()
              (mlp): Mlp(
                (fc1): Linear(in_features=96, out_features=192, bias=True)
                (act): GELU(approximate='none')
                (fc2): Linear(in_features=192, out_features=96, bias=True)
                (drop): Dropout(p=0, inplace=False)
              )
              (norm): LayerNorm((96,), eps=1e-05, elementwise_affine=True)
              (attn): Attention(
                (conv_proj_q): Sequential(
                  (conv): Conv3d(96, 96, kernel_size=(3, 3, 3), stride=(1, 1, 1), padding=(1, 1, 1), groups=96, bias=False)
                  (rearrage): Rearrange('b c t h w -> b (t h w) c')
                  (bn): LayerNorm((96,), eps=1e-05, elementwise_affine=True)
                )
                (conv_proj_k): Sequential(
                  (conv): Conv3d(96, 96, kernel_size=(1, 16, 16), stride=(1, 16, 16), groups=96, bias=False)
                  (rearrage): Rearrange('b c t h w -> b (t h w) c')
                  (bn): LayerNorm((96,), eps=1e-05, elementwise_affine=True)
                )
                (conv_proj_v): Sequential(
                  (conv): Conv3d(96, 96, kernel_size=(1, 16, 16), stride=(1, 16, 16), groups=96, bias=False)
                  (rearrage): Rearrange('b c t h w -> b (t h w) c')
                  (bn): LayerNorm((96,), eps=1e-05, elementwise_affine=True)
                )
                (proj_q): Linear(in_features=96, out_features=96, bias=True)
                (proj_k): Linear(in_features=96, out_features=96, bias=True)
                (proj_v): Linear(in_features=96, out_features=96, bias=True)
                (attn_drop): Dropout(p=0, inplace=False)
                (proj): Linear(in_features=96, out_features=96, bias=True)
                (proj_drop): Dropout(p=0, inplace=False)
              )
              (norm2): LayerNorm((96,), eps=1e-05, elementwise_affine=True)
              (align_conv): Conv2d(512, 96, kernel_size=(1, 1), stride=(1, 1))
            )
          )
        )
      )
      (mt_proj): Sequential(
        (0): Conv2d(768, 96, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
        (1): BatchNorm2d(96, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (2): ReLU(inplace=True)
      )
    )
    (logits): MLPHead(
      (linear_pred): Conv2d(96, 1, kernel_size=(1, 1), stride=(1, 1))
      (sig): Sigmoid()
    )
    (temb): Module(
      (dense): ModuleList(
        (0): Linear(in_features=96, out_features=384, bias=True)
        (1): Linear(in_features=384, out_features=384, bias=True)
      )
    )
    (conv_in): Conv2d(1, 96, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
    (down1): Downsample4x4(
      (conv): Conv2d(96, 96, kernel_size=(3, 3), stride=(4, 4))
    )
    (res_encoder): ModuleList(
      (0): Sequential(
        (0): ResnetBlock(
          (norm1): GroupNorm(32, 96, eps=1e-06, affine=True)
          (conv1): Conv2d(96, 192, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (temb_proj): Linear(in_features=384, out_features=192, bias=True)
          (norm2): GroupNorm(32, 192, eps=1e-06, affine=True)
          (dropout): Dropout(p=0.1, inplace=False)
          (conv2): Conv2d(192, 192, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (nin_shortcut): Conv2d(96, 192, kernel_size=(1, 1), stride=(1, 1))
        )
        (1): Downsample(
          (conv): Conv2d(192, 192, kernel_size=(3, 3), stride=(2, 2))
        )
      )
      (1): Sequential(
        (0): ResnetBlock(
          (norm1): GroupNorm(32, 192, eps=1e-06, affine=True)
          (conv1): Conv2d(192, 384, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (temb_proj): Linear(in_features=384, out_features=384, bias=True)
          (norm2): GroupNorm(32, 384, eps=1e-06, affine=True)
          (dropout): Dropout(p=0.1, inplace=False)
          (conv2): Conv2d(384, 384, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (nin_shortcut): Conv2d(192, 384, kernel_size=(1, 1), stride=(1, 1))
        )
        (1): Downsample(
          (conv): Conv2d(384, 384, kernel_size=(3, 3), stride=(2, 2))
        )
      )
      (2): Sequential(
        (0): ResnetBlock(
          (norm1): GroupNorm(32, 384, eps=1e-06, affine=True)
          (conv1): Conv2d(384, 768, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (temb_proj): Linear(in_features=384, out_features=768, bias=True)
          (norm2): GroupNorm(32, 768, eps=1e-06, affine=True)
          (dropout): Dropout(p=0.1, inplace=False)
          (conv2): Conv2d(768, 768, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (nin_shortcut): Conv2d(384, 768, kernel_size=(1, 1), stride=(1, 1))
        )
        (1): Downsample(
          (conv): Conv2d(768, 768, kernel_size=(3, 3), stride=(2, 2))
        )
      )
    )
  )
)
