"""
Multi-Metric Loss Functions: 多指标损失函数
KLDiv + CC + SIM + NSS组合损失，全面优化显著性预测指标
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np


class MultiMetricLoss(nn.Module):
    """
    多指标组合损失函数
    
    结合KL散度、线性相关系数(CC)、相似性(SIM)和NSS等指标，
    全面优化显著性预测的各个方面
    """
    
    def __init__(self, 
                 kl_weight=1.0, 
                 cc_weight=1.0, 
                 sim_weight=1.0, 
                 nss_weight=1.0,
                 l1_weight=0.0,
                 mse_weight=0.0,
                 normalize_pred=True,
                 normalize_gt=True,
                 eps=1e-8):
        super().__init__()
        
        self.kl_weight = kl_weight
        self.cc_weight = cc_weight
        self.sim_weight = sim_weight
        self.nss_weight = nss_weight
        self.l1_weight = l1_weight
        self.mse_weight = mse_weight
        
        self.normalize_pred = normalize_pred
        self.normalize_gt = normalize_gt
        self.eps = eps
        
        # 各个损失组件
        self.kl_loss = KLDivergenceLoss(eps=eps)
        self.cc_loss = CorrelationCoefficientLoss(eps=eps)
        self.sim_loss = SimilarityLoss(eps=eps)
        self.nss_loss = NSSLoss(eps=eps)
    
    def forward(self, pred_sal, gt_sal, fixation_map=None):
        """
        前向传播计算损失
        
        Args:
            pred_sal: [B, 1, H, W] - 预测的显著性图
            gt_sal: [B, 1, H, W] - 真实显著性图（密度图）
            fixation_map: [B, 1, H, W] - 注视点图（可选，用于NSS）
            
        Returns:
            loss_dict: dict - 包含各个损失分量的字典
        """
        # 预处理
        if self.normalize_pred:
            pred_sal = self._normalize_saliency_map(pred_sal)
        
        if self.normalize_gt:
            gt_sal = self._normalize_saliency_map(gt_sal)
        
        # 如果没有提供fixation_map，使用gt_sal作为近似
        if fixation_map is None:
            fixation_map = gt_sal
        
        loss_dict = {}
        total_loss = 0.0
        
        # KL散度损失
        if self.kl_weight > 0:
            kl_loss = self.kl_loss(pred_sal, gt_sal)
            loss_dict['kl_loss'] = kl_loss
            total_loss += self.kl_weight * kl_loss
        
        # 相关系数损失 (1 - CC)
        if self.cc_weight > 0:
            cc_loss = self.cc_loss(pred_sal, gt_sal)
            loss_dict['cc_loss'] = cc_loss
            total_loss += self.cc_weight * cc_loss
        
        # 相似性损失 (1 - SIM)
        if self.sim_weight > 0:
            sim_loss = self.sim_loss(pred_sal, gt_sal)
            loss_dict['sim_loss'] = sim_loss
            total_loss += self.sim_weight * sim_loss
        
        # NSS损失 (-NSS)
        if self.nss_weight > 0:
            nss_loss = self.nss_loss(pred_sal, fixation_map)
            loss_dict['nss_loss'] = nss_loss
            total_loss += self.nss_weight * nss_loss
        
        # 额外的正则化损失
        if self.l1_weight > 0:
            l1_loss = F.l1_loss(pred_sal, gt_sal)
            loss_dict['l1_loss'] = l1_loss
            total_loss += self.l1_weight * l1_loss
        
        if self.mse_weight > 0:
            mse_loss = F.mse_loss(pred_sal, gt_sal)
            loss_dict['mse_loss'] = mse_loss
            total_loss += self.mse_weight * mse_loss
        
        loss_dict['total_loss'] = total_loss
        
        return loss_dict
    
    def _normalize_saliency_map(self, sal_map):
        """
        归一化显著性图到概率分布
        
        Args:
            sal_map: [B, 1, H, W] - 输入显著性图
            
        Returns:
            normalized_map: [B, 1, H, W] - 归一化后的显著性图
        """
        B, C, H, W = sal_map.shape
        sal_map_flat = sal_map.view(B, -1)
        
        # 最小-最大归一化
        sal_min = sal_map_flat.min(dim=1, keepdim=True)[0]
        sal_max = sal_map_flat.max(dim=1, keepdim=True)[0]
        sal_range = sal_max - sal_min + self.eps
        
        normalized_flat = (sal_map_flat - sal_min) / sal_range
        
        # 归一化为概率分布（和为1）
        normalized_sum = normalized_flat.sum(dim=1, keepdim=True) + self.eps
        normalized_flat = normalized_flat / normalized_sum
        
        return normalized_flat.view(B, C, H, W)


class KLDivergenceLoss(nn.Module):
    """KL散度损失"""
    
    def __init__(self, eps=1e-8):
        super().__init__()
        self.eps = eps
    
    def forward(self, pred_sal, gt_sal):
        """
        计算KL散度
        
        Args:
            pred_sal: [B, 1, H, W] - 预测显著性图
            gt_sal: [B, 1, H, W] - 真实显著性图
            
        Returns:
            kl_loss: scalar - KL散度损失
        """
        B = pred_sal.size(0)
        
        # 展平并归一化为概率分布
        pred_flat = pred_sal.view(B, -1)
        gt_flat = gt_sal.view(B, -1)
        
        # 确保和为1（概率分布）
        pred_flat = pred_flat / (pred_flat.sum(dim=1, keepdim=True) + self.eps)
        gt_flat = gt_flat / (gt_flat.sum(dim=1, keepdim=True) + self.eps)
        
        # 避免log(0)
        pred_flat = torch.clamp(pred_flat, min=self.eps)
        gt_flat = torch.clamp(gt_flat, min=self.eps)
        
        # KL散度: KL(P||Q) = sum(P * log(P/Q))
        kl_div = gt_flat * torch.log(gt_flat / pred_flat)
        kl_loss = kl_div.sum(dim=1).mean()
        
        return kl_loss


class CorrelationCoefficientLoss(nn.Module):
    """皮尔逊相关系数损失 (1 - CC)"""
    
    def __init__(self, eps=1e-8):
        super().__init__()
        self.eps = eps
    
    def forward(self, pred_sal, gt_sal):
        """
        计算相关系数损失
        
        Args:
            pred_sal: [B, 1, H, W] - 预测显著性图
            gt_sal: [B, 1, H, W] - 真实显著性图
            
        Returns:
            cc_loss: scalar - 相关系数损失 (1 - CC)
        """
        B = pred_sal.size(0)
        
        # 展平
        pred_flat = pred_sal.view(B, -1)
        gt_flat = gt_sal.view(B, -1)
        
        # 计算均值
        pred_mean = pred_flat.mean(dim=1, keepdim=True)
        gt_mean = gt_flat.mean(dim=1, keepdim=True)
        
        # 中心化
        pred_centered = pred_flat - pred_mean
        gt_centered = gt_flat - gt_mean
        
        # 计算相关系数
        numerator = (pred_centered * gt_centered).sum(dim=1)
        pred_std = torch.sqrt((pred_centered ** 2).sum(dim=1) + self.eps)
        gt_std = torch.sqrt((gt_centered ** 2).sum(dim=1) + self.eps)
        denominator = pred_std * gt_std
        
        cc = numerator / (denominator + self.eps)
        cc_loss = 1.0 - cc.mean()  # 1 - CC，使其成为损失函数
        
        return cc_loss


class SimilarityLoss(nn.Module):
    """相似性损失 (1 - SIM)"""
    
    def __init__(self, eps=1e-8):
        super().__init__()
        self.eps = eps
    
    def forward(self, pred_sal, gt_sal):
        """
        计算相似性损失
        
        Args:
            pred_sal: [B, 1, H, W] - 预测显著性图
            gt_sal: [B, 1, H, W] - 真实显著性图
            
        Returns:
            sim_loss: scalar - 相似性损失 (1 - SIM)
        """
        B = pred_sal.size(0)
        
        # 展平并归一化为概率分布
        pred_flat = pred_sal.view(B, -1)
        gt_flat = gt_sal.view(B, -1)
        
        # 归一化为概率分布
        pred_flat = pred_flat / (pred_flat.sum(dim=1, keepdim=True) + self.eps)
        gt_flat = gt_flat / (gt_flat.sum(dim=1, keepdim=True) + self.eps)
        
        # 相似性: SIM = sum(min(P, Q))
        similarity = torch.min(pred_flat, gt_flat).sum(dim=1)
        sim_loss = 1.0 - similarity.mean()  # 1 - SIM，使其成为损失函数
        
        return sim_loss


class NSSLoss(nn.Module):
    """归一化扫视分数(NSS)损失 (-NSS)"""
    
    def __init__(self, eps=1e-8):
        super().__init__()
        self.eps = eps
    
    def forward(self, pred_sal, fixation_map):
        """
        计算NSS损失
        
        Args:
            pred_sal: [B, 1, H, W] - 预测显著性图
            fixation_map: [B, 1, H, W] - 注视点二值图
            
        Returns:
            nss_loss: scalar - NSS损失 (-NSS)
        """
        B = pred_sal.size(0)
        
        # 展平
        pred_flat = pred_sal.view(B, -1)
        fix_flat = fixation_map.view(B, -1)
        
        # 标准化预测图（z-score）
        pred_mean = pred_flat.mean(dim=1, keepdim=True)
        pred_std = pred_flat.std(dim=1, keepdim=True) + self.eps
        pred_normalized = (pred_flat - pred_mean) / pred_std
        
        # 计算NSS：注视点处的平均标准化显著性值
        fixation_mask = fix_flat > 0.5  # 二值化注视点图
        
        nss_scores = []
        for i in range(B):
            if fixation_mask[i].sum() > 0:
                # 只在有注视点的位置计算NSS
                nss = pred_normalized[i][fixation_mask[i]].mean()
            else:
                # 如果没有注视点，NSS为0
                nss = torch.tensor(0.0, device=pred_sal.device)
            nss_scores.append(nss)
        
        nss_score = torch.stack(nss_scores).mean()
        nss_loss = -nss_score  # 负NSS作为损失函数
        
        return nss_loss


class MultiScaleLoss(nn.Module):
    """
    多尺度损失
    
    在不同分辨率上计算损失，促进多尺度特征学习
    """
    
    def __init__(self, base_loss, scales=[1.0, 0.5, 0.25], weights=[1.0, 0.5, 0.25]):
        super().__init__()
        
        self.base_loss = base_loss
        self.scales = scales
        self.weights = weights
        
        assert len(scales) == len(weights), "scales and weights must have same length"
    
    def forward(self, pred_sal, gt_sal, fixation_map=None):
        """
        前向传播计算多尺度损失
        
        Args:
            pred_sal: [B, 1, H, W] - 预测显著性图
            gt_sal: [B, 1, H, W] - 真实显著性图
            fixation_map: [B, 1, H, W] - 注视点图（可选）
            
        Returns:
            loss_dict: dict - 包含各尺度损失的字典
        """
        total_loss = 0.0
        loss_dict = {}
        
        for i, (scale, weight) in enumerate(zip(self.scales, self.weights)):
            if scale == 1.0:
                # 原尺度
                scale_pred = pred_sal
                scale_gt = gt_sal
                scale_fix = fixation_map
            else:
                # 下采样到指定尺度
                size = (int(pred_sal.size(2) * scale), int(pred_sal.size(3) * scale))
                scale_pred = F.interpolate(pred_sal, size=size, mode='bilinear', align_corners=False)
                scale_gt = F.interpolate(gt_sal, size=size, mode='bilinear', align_corners=False)
                
                if fixation_map is not None:
                    scale_fix = F.interpolate(fixation_map, size=size, mode='nearest')
                else:
                    scale_fix = None
            
            # 计算该尺度的损失
            scale_loss_dict = self.base_loss(scale_pred, scale_gt, scale_fix)
            scale_loss = scale_loss_dict['total_loss']
            
            # 加权累加
            total_loss += weight * scale_loss
            
            # 记录各尺度损失
            for key, value in scale_loss_dict.items():
                loss_dict[f'scale_{i}_{key}'] = value
        
        loss_dict['multiscale_total_loss'] = total_loss
        
        return loss_dict


def create_multi_metric_loss(kl_weight=1.0, cc_weight=1.0, sim_weight=1.0, nss_weight=1.0, **kwargs):
    """创建多指标损失函数的便捷函数"""
    return MultiMetricLoss(
        kl_weight=kl_weight,
        cc_weight=cc_weight,
        sim_weight=sim_weight,
        nss_weight=nss_weight,
        **kwargs
    )


def create_multiscale_multi_metric_loss(scales=[1.0, 0.5, 0.25], weights=[1.0, 0.5, 0.25], **kwargs):
    """创建多尺度多指标损失函数的便捷函数"""
    base_loss = create_multi_metric_loss(**kwargs)
    return MultiScaleLoss(base_loss, scales=scales, weights=weights)


# 预定义配置
def create_kl_dominant_loss(**kwargs):
    """KL散度主导的损失函数"""
    return create_multi_metric_loss(
        kl_weight=2.0,
        cc_weight=0.5,
        sim_weight=0.5,
        nss_weight=1.0,
        **kwargs
    )


def create_balanced_loss(**kwargs):
    """平衡的多指标损失函数"""
    return create_multi_metric_loss(
        kl_weight=1.0,
        cc_weight=1.0,
        sim_weight=1.0,
        nss_weight=1.0,
        **kwargs
    )


def create_nss_dominant_loss(**kwargs):
    """NSS主导的损失函数"""
    return create_multi_metric_loss(
        kl_weight=0.5,
        cc_weight=0.5,
        sim_weight=0.5,
        nss_weight=2.0,
        **kwargs
    )