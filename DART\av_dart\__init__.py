"""
AV-DART: Audio-Visual Dynamic Adaptive Region Tokenizer

基于DART的音视频动态tokenizer，支持：
- 视觉分支：图像/视频自适应分块
- 音频分支：log-mel谱图自适应分块  
- 跨模态预算：固定总token数量
- A→V门控：音频能量驱动视觉token分配
"""

# 原有组件
from .tokenizers.visual_tokenizer import VisualDARTTokenizer
from .tokenizers.audio_tokenizer import AudioDARTTokenizer
from .tokenizers.av_tokenizer import AVTokenizer
from .adapters.dart_adapter import DARTAdapter
from .models.budget import BudgetController
from .utils.posenc import PositionalEncoding

# 新增核心组件
from .tokenizers.enhanced_av_tokenizer import EnhancedAVTokenizer, create_enhanced_av_tokenizer
from .models.av_dart_model import AVDARTModel, create_av_dart_model, create_av_dart_small, create_av_dart_base, create_av_dart_large
from .models.saliency_decoder import SaliencyDecoder, create_saliency_decoder, create_lightweight_decoder, create_enhanced_decoder, create_high_res_decoder

__version__ = "0.1.0"
__all__ = [
    # 原有组件
    "VisualDARTTokenizer",
    "AudioDARTTokenizer", 
    "AVTokenizer",
    "DARTAdapter",
    "BudgetController",
    "PositionalEncoding",
    # 新增核心组件
    "EnhancedAVTokenizer",
    "AVDARTModel", 
    "SaliencyDecoder",
    # 便捷创建函数
    "create_enhanced_av_tokenizer",
    "create_av_dart_model",
    "create_av_dart_small",
    "create_av_dart_base",
    "create_av_dart_large",
    "create_saliency_decoder",
    "create_lightweight_decoder",
    "create_enhanced_decoder",
    "create_high_res_decoder"
]