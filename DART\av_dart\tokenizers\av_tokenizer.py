"""
音视频融合Tokenizer

AV-DART的核心模块，负责：
- 整合视觉和音频tokenizer
- 实现跨模态预算分配
- A→V门控调节
- 统一的音视频token序列输出
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Optional, Tuple, Union, Dict, List
import sys
import os

# 添加相对路径
sys.path.append(os.path.dirname(__file__))
from .visual_tokenizer import VisualDARTTokenizer, TemporalVisualTokenizer
from .audio_tokenizer import AudioDARTTokenizer, SpectralAudioTokenizer
from ..models.budget import BudgetController


import os
# print("当前工作目录:", os.getcwd())
# print("期望的数据路径:", os.path.abspath("./data/fold_lists/DIEM_list_train_fps.txt"))



class AVTokenizer(nn.Module):
    """
    音视频融合Tokenizer (AV-DART核心)
    
    功能：
    1. 协调视觉和音频tokenizer
    2. 跨模态预算分配：保证 N_total = N_v + N_a 固定
    3. A→V门控：音频能量驱动视觉token分配
    4. 输出统一的音视频token序列
    
    Args:
        visual_config: 视觉tokenizer配置
        audio_config: 音频tokenizer配置
        total_tokens: 总token预算，默认196
        budget_strategy: 预算分配策略
        enable_cross_attention: 是否启用跨模态注意力
        fusion_method: 融合方法 "concat", "interleave", "attention"
    """
    
    def __init__(
        self,
        visual_config: Dict = None,
        audio_config: Dict = None,
        total_tokens: int = 196,
        budget_strategy: str = "dynamic",  # "fixed", "dynamic", "adaptive"
        enable_cross_attention: bool = False,
        fusion_method: str = "concat",  # "concat", "interleave", "attention"
        av_align_method: str = "energy_gate",  # "energy_gate", "attention_gate", "none"
        **kwargs
    ):
        super().__init__()
        
        self.total_tokens = total_tokens
        self.budget_strategy = budget_strategy
        self.enable_cross_attention = enable_cross_attention
        self.fusion_method = fusion_method
        self.av_align_method = av_align_method
        
        # 默认配置
        visual_config = visual_config or {}
        audio_config = audio_config or {}
        
        # 确保embed_dim一致
        embed_dim = visual_config.get('embed_dim', 768)
        audio_config['embed_dim'] = embed_dim
        visual_config['embed_dim'] = embed_dim
        self.embed_dim = embed_dim
        
        # 视觉tokenizer
        if visual_config.get('temporal_mode', False):
            self.visual_tokenizer = TemporalVisualTokenizer(**visual_config)
        else:
            # 处理enable_audio_gating参数冲突
            visual_config_copy = visual_config.copy()
            # 如果visual_config中没有显式设置enable_audio_gating，则根据av_align_method设置
            if 'enable_audio_gating' not in visual_config_copy:
                visual_config_copy['enable_audio_gating'] = (av_align_method != "none")
            
            self.visual_tokenizer = VisualDARTTokenizer(**visual_config_copy)
        
        # 音频tokenizer  
        if audio_config.get('spectral_mode', False):
            self.audio_tokenizer = SpectralAudioTokenizer(**audio_config)
        else:
            self.audio_tokenizer = AudioDARTTokenizer(**audio_config)
        
        # 预算控制器
        self.budget_controller = BudgetController(
            total_tokens=total_tokens,
            strategy=budget_strategy,
            visual_default=self.visual_tokenizer.get_num_patches_default(),
            audio_default=self.audio_tokenizer.get_num_patches_default()
        )
        
        # 跨模态对齐模块
        if av_align_method == "attention_gate":
            self.av_align = CrossModalAttentionGate(embed_dim)
        elif av_align_method == "energy_gate":
            self.av_align = EnergyGate(embed_dim)
        else:
            self.av_align = None
        
        # 跨模态注意力（可选）
        if enable_cross_attention:
            self.cross_attention = CrossModalAttention(embed_dim)
        
        # 融合模块
        if fusion_method == "attention":
            self.fusion_attention = nn.MultiheadAttention(embed_dim, num_heads=8, batch_first=True)
        
        # 模态类型嵌入
        self.modal_type_embed = nn.Parameter(torch.zeros(2, embed_dim))  # [visual, audio]
        nn.init.trunc_normal_(self.modal_type_embed, std=0.02)
    
    def forward(
        self,
        visual_input: torch.Tensor,
        audio_input: Union[torch.Tensor, Dict],
        # budget_info: Optional[Dict] = None,
        return_dict: bool = False
    ) -> Union[Tuple[torch.Tensor, torch.Tensor], Dict]:
        """
        音视频联合tokenization
        
        Args:
            visual_input: 视觉输入 [B, C, H, W] 或 [B, T, C, H, W]
            audio_input: 音频输入（谱图或波形或字典）
            return_dict: 是否返回详细信息
            
        Returns:
            av_tokens: 融合的音视频tokens [B, N_total, D]
            av_positions: 位置编码 [B, N_total, D]
            或详细信息字典
        """
        B = visual_input.size(0)
        
        # === 阶段1：预算分配 ===
        # budget_info = self.budget_controller(visual_input, audio_input)
        # # if budget_info is None:
        # #     # 默认分配，比如视觉 64 个，音频 16 个，总共 80
        # #     budget_info = {'visual_tokens': 64, 'audio_tokens': 16}
        # N_v = budget_info['visual_tokens']
        # N_a = budget_info['audio_tokens']
        
        budget_info = self.budget_controller(visual_input, audio_input)

        # 兼容不同命名
        if "visual_tokens" in budget_info:
            N_v = budget_info["visual_tokens"]
            N_a = budget_info["audio_tokens"]
        elif "N_v" in budget_info:
            N_v = budget_info["N_v"]
            N_a = budget_info["N_a"]
        else:
            raise KeyError(f"Unknown budget_info keys: {budget_info.keys()}")


        # === 阶段2：音频tokenization + 能量提取 ===
        audio_result = self.audio_tokenizer(
            audio_input,
            num_patches=N_a,
            return_dict=True
        )
        
        audio_tokens = audio_result['audio_tokens']  # [B, N_a, D]
        audio_pos = audio_result['pos_encoding']
        audio_energy = audio_result.get('audio_energy', None)  # [B, T]
        
        # === 阶段3：视觉tokenization + 音频门控 ===
        visual_result = self.visual_tokenizer(
            visual_input,
            num_patches=N_v,
            audio_energy=audio_energy,
            return_dict=True
        )
        
        visual_tokens = visual_result['visual_tokens']  # [B, N_v, D] 或 [B, T, N_v, D]
        visual_pos = visual_result['pos_encoding']
        
        # 处理时序视觉tokens
        if visual_tokens.dim() == 4:  # [B, T, N_v, D]
            B, T, N_v_per_frame, D = visual_tokens.shape
            visual_tokens = visual_tokens.reshape(B, T * N_v_per_frame, D)
            visual_pos = visual_pos.reshape(B, T * N_v_per_frame, D)
            N_v = T * N_v_per_frame
        
        # === 阶段4：跨模态对齐 ===
        if self.av_align is not None:
            visual_tokens, audio_tokens = self.av_align(
                visual_tokens, audio_tokens, audio_energy
            )
        
        # === 阶段5：跨模态注意力（可选）===
        if self.enable_cross_attention:
            visual_tokens, audio_tokens = self.cross_attention(visual_tokens, audio_tokens)
        
        # === 阶段6：模态类型嵌入 ===
        visual_tokens = visual_tokens + self.modal_type_embed[0].unsqueeze(0).unsqueeze(0)
        audio_tokens = audio_tokens + self.modal_type_embed[1].unsqueeze(0).unsqueeze(0)
        
        # === 阶段7：序列融合 ===
        av_tokens, av_positions = self.fuse_modalities(
            visual_tokens, audio_tokens, visual_pos, audio_pos
        )
        
        if return_dict:
            output = {
                'av_tokens': av_tokens,
                'av_positions': av_positions,
                'visual_tokens': visual_tokens,
                'audio_tokens': audio_tokens,
                'visual_positions': visual_pos,
                'audio_positions': audio_pos,
                'budget_allocation': {'N_v': N_v, 'N_a': N_a, 'N_total': N_v + N_a},
                'audio_energy': audio_energy,
                'visual_importance': visual_result.get('importance_map', None),
                'audio_importance': audio_result.get('importance_map', None)
            }
            return output
        else:
            return av_tokens, av_positions
    
    def fuse_modalities(
        self,
        visual_tokens: torch.Tensor,
        audio_tokens: torch.Tensor, 
        visual_pos: torch.Tensor,
        audio_pos: torch.Tensor
    ) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        融合视觉和音频tokens
        
        Args:
            visual_tokens: [B, N_v, D]
            audio_tokens: [B, N_a, D]
            visual_pos: [B, N_v, D]
            audio_pos: [B, N_a, D]
            
        Returns:
            fused_tokens: [B, N_total, D]
            fused_positions: [B, N_total, D]
        """
        if self.fusion_method == "concat":
            # 简单拼接：[visual_tokens, audio_tokens]
            fused_tokens = torch.cat([visual_tokens, audio_tokens], dim=1)
            fused_positions = torch.cat([visual_pos, audio_pos], dim=1)
            
        elif self.fusion_method == "interleave":
            # 交错排列：v1, a1, v2, a2, ...
            fused_tokens, fused_positions = self.interleave_tokens(
                visual_tokens, audio_tokens, visual_pos, audio_pos
            )
            
        elif self.fusion_method == "attention":
            # 注意力融合
            all_tokens = torch.cat([visual_tokens, audio_tokens], dim=1)
            all_positions = torch.cat([visual_pos, audio_pos], dim=1)
            
            # 自注意力重新排列
            fused_tokens, _ = self.fusion_attention(all_tokens, all_tokens, all_tokens)
            fused_positions = all_positions  # 位置编码保持不变
            
        else:
            raise ValueError(f"Unknown fusion_method: {self.fusion_method}")
        
        return fused_tokens, fused_positions
    
    def interleave_tokens(
        self,
        visual_tokens: torch.Tensor,
        audio_tokens: torch.Tensor,
        visual_pos: torch.Tensor,
        audio_pos: torch.Tensor
    ) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        交错排列tokens
        
        策略：根据tokens数量比例决定交错模式
        """
        B, N_v, D = visual_tokens.shape
        B, N_a, D = audio_tokens.shape
        
        if N_v == N_a:
            # 1:1交错
            fused_tokens = torch.stack([visual_tokens, audio_tokens], dim=2)  # [B, N, 2, D]
            fused_tokens = fused_tokens.view(B, N_v * 2, D)
            
            fused_positions = torch.stack([visual_pos, audio_pos], dim=2)
            fused_positions = fused_positions.view(B, N_v * 2, D)
            
        else:
            # 不等长交错：先放数量多的
            if N_v > N_a:
                primary_tokens, secondary_tokens = visual_tokens, audio_tokens
                primary_pos, secondary_pos = visual_pos, audio_pos
            else:
                primary_tokens, secondary_tokens = audio_tokens, visual_tokens
                primary_pos, secondary_pos = audio_pos, visual_pos
            
            # 计算交错步长
            step = max(N_v, N_a) // min(N_v, N_a)
            
            fused_tokens = primary_tokens.clone()
            fused_positions = primary_pos.clone()
            
            # 插入secondary tokens
            for i, idx in enumerate(range(step, max(N_v, N_a), step)):
                if i < min(N_v, N_a):
                    # 在指定位置插入
                    left = fused_tokens[:, :idx + i]
                    right = fused_tokens[:, idx + i:]
                    insert_token = secondary_tokens[:, i:i+1]
                    fused_tokens = torch.cat([left, insert_token, right], dim=1)
                    
                    left_pos = fused_positions[:, :idx + i]
                    right_pos = fused_positions[:, idx + i:]
                    insert_pos = secondary_pos[:, i:i+1]
                    fused_positions = torch.cat([left_pos, insert_pos, right_pos], dim=1)
        
        return fused_tokens, fused_positions


class CrossModalAttentionGate(nn.Module):
    """
    跨模态注意力门控
    
    使用注意力机制实现音频到视觉的信息传递
    """
    
    def __init__(self, embed_dim: int, num_heads: int = 4):
        super().__init__()
        
        self.embed_dim = embed_dim
        self.num_heads = num_heads
        
        # 音频->视觉注意力
        self.audio_to_visual = nn.MultiheadAttention(
            embed_dim, num_heads, batch_first=True
        )
        
        # 门控网络
        self.gate_proj = nn.Sequential(
            nn.Linear(embed_dim, embed_dim // 4),
            nn.ReLU(),
            nn.Linear(embed_dim // 4, embed_dim),
            nn.Sigmoid()
        )
    
    def forward(
        self,
        visual_tokens: torch.Tensor,
        audio_tokens: torch.Tensor,
        audio_energy: Optional[torch.Tensor] = None
    ) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        Args:
            visual_tokens: [B, N_v, D]
            audio_tokens: [B, N_a, D] 
            audio_energy: [B, T] (可选)
            
        Returns:
            enhanced_visual: [B, N_v, D]
            audio_tokens: [B, N_a, D] (unchanged)
        """
        # 音频->视觉跨模态注意力
        audio_context, _ = self.audio_to_visual(
            visual_tokens,  # query
            audio_tokens,   # key
            audio_tokens    # value
        )
        
        # 生成门控权重
        gate_weights = self.gate_proj(audio_context)
        
        # 应用门控
        enhanced_visual = visual_tokens * gate_weights + visual_tokens * (1 - gate_weights)
        
        return enhanced_visual, audio_tokens


class EnergyGate(nn.Module):
    """
    基于音频能量的门控
    
    直接使用音频能量特征调节视觉表示
    """
    
    def __init__(self, embed_dim: int):
        super().__init__()
        
        self.embed_dim = embed_dim
        
        # 能量处理网络
        self.energy_proj = nn.Sequential(
            nn.Linear(1, embed_dim // 8),
            nn.ReLU(),
            nn.Linear(embed_dim // 8, embed_dim),
            nn.Sigmoid()
        )
    
    def forward(
        self,
        visual_tokens: torch.Tensor,
        audio_tokens: torch.Tensor,
        audio_energy: Optional[torch.Tensor] = None
    ) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        Args:
            visual_tokens: [B, N_v, D]
            audio_tokens: [B, N_a, D]
            audio_energy: [B, T]
            
        Returns:
            gated_visual: [B, N_v, D]
            audio_tokens: [B, N_a, D] (unchanged)
        """
        if audio_energy is None:
            return visual_tokens, audio_tokens
        
        B, T = audio_energy.shape
        B, N_v, D = visual_tokens.shape
        
        # 平均音频能量
        avg_energy = audio_energy.mean(dim=1, keepdim=True)  # [B, 1]
        
        # 生成门控权重
        gate_weights = self.energy_proj(avg_energy.unsqueeze(-1))  # 可能是 [B, 1, D] 或 [B, D]
        gate_weights = gate_weights.view(B, -1)  # 强制拉平为 [B, D]
        gate_weights = gate_weights.unsqueeze(1).expand(-1, N_v, -1)  # [B, N_v, D]
        
        # 应用门控
        gated_visual = visual_tokens * gate_weights
        
        return gated_visual, audio_tokens


class CrossModalAttention(nn.Module):
    """
    双向跨模态注意力
    
    实现视觉和音频特征的双向信息交换
    """
    
    def __init__(self, embed_dim: int, num_heads: int = 8, dropout: float = 0.1):
        super().__init__()
        
        self.embed_dim = embed_dim
        self.num_heads = num_heads
        
        # 视觉->音频注意力
        self.visual_to_audio = nn.MultiheadAttention(
            embed_dim, num_heads, dropout=dropout, batch_first=True
        )
        
        # 音频->视觉注意力  
        self.audio_to_visual = nn.MultiheadAttention(
            embed_dim, num_heads, dropout=dropout, batch_first=True
        )
        
        # 残差连接和层归一化
        self.visual_norm = nn.LayerNorm(embed_dim)
        self.audio_norm = nn.LayerNorm(embed_dim)
        
        self.dropout = nn.Dropout(dropout)
    
    def forward(
        self,
        visual_tokens: torch.Tensor,
        audio_tokens: torch.Tensor
    ) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        双向跨模态注意力
        
        Args:
            visual_tokens: [B, N_v, D]
            audio_tokens: [B, N_a, D]
            
        Returns:
            enhanced_visual: [B, N_v, D]
            enhanced_audio: [B, N_a, D]
        """
        # 视觉->音频
        audio_enhanced, _ = self.visual_to_audio(
            audio_tokens,    # query
            visual_tokens,   # key
            visual_tokens    # value
        )
        audio_enhanced = self.audio_norm(
            audio_tokens + self.dropout(audio_enhanced)
        )
        
        # 音频->视觉
        visual_enhanced, _ = self.audio_to_visual(
            visual_tokens,   # query
            audio_tokens,    # key  
            audio_tokens     # value
        )
        visual_enhanced = self.visual_norm(
            visual_tokens + self.dropout(visual_enhanced)
        )
        
        return visual_enhanced, audio_enhanced


# 便利函数
def create_av_tokenizer(
    visual_config: Dict = None,
    audio_config: Dict = None,
    total_tokens: int = 196,
    budget_strategy: str = "dynamic",
    enable_cross_attention: bool = False,
    fusion_method: str = "concat",
    av_align_method: str = "energy_gate",
    **kwargs
) -> AVTokenizer:
    """
    创建AV-DART tokenizer的便利函数
    
    Args:
        visual_config: 视觉配置字典
        audio_config: 音频配置字典
        total_tokens: 总token预算
        budget_strategy: 预算分配策略
        enable_cross_attention: 跨模态注意力
        fusion_method: 融合方法
        av_align_method: 对齐方法
        **kwargs: 其他参数
        
    Returns:
        AV-DART tokenizer实例
    """
    return AVTokenizer(
        visual_config=visual_config,
        audio_config=audio_config,
        total_tokens=total_tokens,
        budget_strategy=budget_strategy,
        enable_cross_attention=enable_cross_attention,
        fusion_method=fusion_method,
        av_align_method=av_align_method,
        **kwargs
    )


def create_simple_av_tokenizer(
    embed_dim: int = 768,
    total_tokens: int = 196,
    visual_size: int = 224,
    audio_size: Tuple[int, int] = (64, 224)
) -> AVTokenizer:
    """
    创建简单AV-DART tokenizer（占位版本）
    
    Args:
        embed_dim: 嵌入维度
        total_tokens: 总token数
        visual_size: 视觉输入尺寸
        audio_size: 音频输入尺寸
        
    Returns:
        简化的AV-DART tokenizer
    """
    visual_config = {
        'input_size': visual_size,
        'embed_dim': embed_dim,
        'enable_audio_gating': True
    }
    
    audio_config = {
        'input_size': audio_size,
        'embed_dim': embed_dim,
        'enable_energy_extraction': True
    }
    
    return AVTokenizer(
        visual_config=visual_config,
        audio_config=audio_config,
        total_tokens=total_tokens,
        budget_strategy="fixed",  # 占位版使用固定预算
        enable_cross_attention=False,
        fusion_method="concat",
        av_align_method="energy_gate"
    )