"""
位置编码模块

为AV-DART提供各种位置编码方案：
- 正弦位置编码
- 可学习位置编码
- 相对位置编码
- 时-频域特定位置编码
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import math
from typing import Optional, Tuple


class PositionalEncoding(nn.Module):
    """
    标准正弦位置编码
    
    适用于序列建模，提供绝对位置信息
    """
    
    def __init__(
        self,
        embed_dim: int,
        max_len: int = 5000,
        dropout: float = 0.1
    ):
        super().__init__()
        
        self.embed_dim = embed_dim
        self.dropout = nn.Dropout(dropout)
        
        # 创建位置编码矩阵
        pe = torch.zeros(max_len, embed_dim)
        position = torch.arange(0, max_len, dtype=torch.float).unsqueeze(1)
        
        div_term = torch.exp(torch.arange(0, embed_dim, 2).float() * 
                           (-math.log(10000.0) / embed_dim))
        
        pe[:, 0::2] = torch.sin(position * div_term)
        pe[:, 1::2] = torch.cos(position * div_term)
        
        self.register_buffer('pe', pe.unsqueeze(0))  # [1, max_len, embed_dim]
    
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """
        Args:
            x: [B, N, D] 输入tokens
            
        Returns:
            x: [B, N, D] 添加位置编码后的tokens
        """
        B, N, D = x.shape
        x = x + self.pe[:, :N, :]
        return self.dropout(x)


class LearnablePositionalEncoding(nn.Module):
    """
    可学习位置编码
    
    通过训练学习最优的位置表示
    """
    
    def __init__(
        self,
        embed_dim: int,
        max_len: int = 1000,
        dropout: float = 0.1
    ):
        super().__init__()
        
        self.embed_dim = embed_dim
        self.dropout = nn.Dropout(dropout)
        
        # 可学习的位置嵌入
        self.pos_embedding = nn.Parameter(torch.zeros(1, max_len, embed_dim))
        nn.init.trunc_normal_(self.pos_embedding, std=0.02)
    
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """
        Args:
            x: [B, N, D] 输入tokens
            
        Returns:
            x: [B, N, D] 添加位置编码后的tokens
        """
        B, N, D = x.shape
        x = x + self.pos_embedding[:, :N, :]
        return self.dropout(x)


class RelativePositionalEncoding(nn.Module):
    """
    相对位置编码
    
    编码tokens之间的相对位置关系而不是绝对位置
    """
    
    def __init__(
        self,
        embed_dim: int,
        max_relative_distance: int = 64
    ):
        super().__init__()
        
        self.embed_dim = embed_dim
        self.max_relative_distance = max_relative_distance
        
        # 相对位置嵌入表
        self.relative_pos_embed = nn.Parameter(
            torch.zeros(2 * max_relative_distance + 1, embed_dim)
        )
        nn.init.trunc_normal_(self.relative_pos_embed, std=0.02)
    
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """
        Args:
            x: [B, N, D] 输入tokens
            
        Returns:
            rel_pos_bias: [N, N] 相对位置偏置矩阵
        """
        B, N, D = x.shape
        
        # 创建相对位置矩阵
        positions = torch.arange(N, device=x.device)
        relative_positions = positions.unsqueeze(0) - positions.unsqueeze(1)  # [N, N]
        
        # 限制相对距离范围
        relative_positions = torch.clamp(
            relative_positions,
            -self.max_relative_distance,
            self.max_relative_distance
        )
        
        # 转换为正索引
        relative_positions = relative_positions + self.max_relative_distance
        
        # 获取相对位置嵌入
        rel_pos_embed = self.relative_pos_embed[relative_positions]  # [N, N, D]
        
        # 计算位置偏置 
        pos_bias = torch.einsum('nnd,bnd->bn', rel_pos_embed, x)  # [B, N]
        
        return pos_bias.unsqueeze(1).expand(-1, N, -1)  # [B, N, N]


class SpatialPositionalEncoding(nn.Module):
    """
    空间位置编码
    
    专门为2D空间数据（如图像patches）设计的位置编码
    编码x, y坐标信息
    """
    
    def __init__(
        self,
        embed_dim: int,
        max_height: int = 56,
        max_width: int = 56
    ):
        super().__init__()
        
        self.embed_dim = embed_dim
        self.max_height = max_height
        self.max_width = max_width
        
        # 为x, y坐标各分配一半的embedding维度
        assert embed_dim % 2 == 0, "embed_dim must be even for spatial encoding"
        
        half_dim = embed_dim // 2
        
        # x坐标编码
        self.x_pos_embed = nn.Parameter(torch.zeros(max_width, half_dim))
        # y坐标编码
        self.y_pos_embed = nn.Parameter(torch.zeros(max_height, half_dim))
        
        nn.init.trunc_normal_(self.x_pos_embed, std=0.02)
        nn.init.trunc_normal_(self.y_pos_embed, std=0.02)
    
    def forward(
        self, 
        x: torch.Tensor, 
        positions: Optional[torch.Tensor] = None
    ) -> torch.Tensor:
        """
        Args:
            x: [B, N, D] 输入tokens
            positions: [B, N, 2] 空间位置坐标 (x, y)，如果为None则使用网格位置
            
        Returns:
            x: [B, N, D] 添加空间位置编码后的tokens
        """
        B, N, D = x.shape
        
        if positions is None:
            # 假设tokens来自规则网格
            grid_h = int(math.sqrt(N))
            grid_w = N // grid_h
            
            y_pos = torch.arange(grid_h, device=x.device).repeat_interleave(grid_w)
            x_pos = torch.arange(grid_w, device=x.device).repeat(grid_h)
            
            positions = torch.stack([x_pos, y_pos], dim=1)  # [N, 2]
            positions = positions.unsqueeze(0).expand(B, -1, -1)  # [B, N, 2]
        
        # 获取位置编码
        x_coords = positions[:, :, 0].long()  # [B, N]
        y_coords = positions[:, :, 1].long()  # [B, N]
        
        x_embed = self.x_pos_embed[x_coords]  # [B, N, half_dim]
        y_embed = self.y_pos_embed[y_coords]  # [B, N, half_dim]
        
        # 拼接x, y编码
        spatial_embed = torch.cat([x_embed, y_embed], dim=-1)  # [B, N, D]
        
        return x + spatial_embed


class TemporalPositionalEncoding(nn.Module):
    """
    时序位置编码
    
    专门为时序数据设计，支持不同的时间尺度
    """
    
    def __init__(
        self,
        embed_dim: int,
        max_time_steps: int = 1000,
        time_scale: float = 1.0
    ):
        super().__init__()
        
        self.embed_dim = embed_dim
        self.max_time_steps = max_time_steps
        self.time_scale = time_scale
        
        # 时序位置编码表
        self.temporal_embed = nn.Parameter(torch.zeros(max_time_steps, embed_dim))
        nn.init.trunc_normal_(self.temporal_embed, std=0.02)
    
    def forward(
        self, 
        x: torch.Tensor, 
        time_steps: Optional[torch.Tensor] = None
    ) -> torch.Tensor:
        """
        Args:
            x: [B, T, N, D] 或 [B, N, D] 输入tokens
            time_steps: [B, T] 时间步索引，如果为None则使用连续时间步
            
        Returns:
            x: 添加时序位置编码后的tokens
        """
        if x.dim() == 3:  # [B, N, D] -> [B, 1, N, D]
            x = x.unsqueeze(1)
            single_timestep = True
        else:
            single_timestep = False
        
        B, T, N, D = x.shape
        
        if time_steps is None:
            time_steps = torch.arange(T, device=x.device).unsqueeze(0).expand(B, -1)
        
        # 获取时序编码
        time_embed = self.temporal_embed[time_steps]  # [B, T, D]
        time_embed = time_embed.unsqueeze(2).expand(-1, -1, N, -1)  # [B, T, N, D]
        
        x = x + time_embed
        
        if single_timestep:
            x = x.squeeze(1)  # [B, N, D]
        
        return x


class SpectralPositionalEncoding(nn.Module):
    """
    频谱位置编码
    
    专门为音频谱图设计，编码时间-频率位置信息
    """
    
    def __init__(
        self,
        embed_dim: int,
        max_time_frames: int = 200,
        max_freq_bins: int = 256,
        freq_scale: float = 1.0
    ):
        super().__init__()
        
        self.embed_dim = embed_dim
        self.max_time_frames = max_time_frames
        self.max_freq_bins = max_freq_bins
        self.freq_scale = freq_scale
        
        # 时间和频率各占一半维度
        assert embed_dim % 2 == 0, "embed_dim must be even for spectral encoding"
        half_dim = embed_dim // 2
        
        # 时间轴编码
        self.time_embed = nn.Parameter(torch.zeros(max_time_frames, half_dim))
        # 频率轴编码（考虑频率的对数特性）
        self.freq_embed = nn.Parameter(torch.zeros(max_freq_bins, half_dim))
        
        nn.init.trunc_normal_(self.time_embed, std=0.02)
        nn.init.trunc_normal_(self.freq_embed, std=0.02)
    
    def forward(
        self, 
        x: torch.Tensor, 
        time_freq_positions: Optional[torch.Tensor] = None
    ) -> torch.Tensor:
        """
        Args:
            x: [B, N, D] 音频谱图patches的tokens
            time_freq_positions: [B, N, 2] 时-频位置坐标 (time, freq)
            
        Returns:
            x: [B, N, D] 添加谱图位置编码后的tokens
        """
        B, N, D = x.shape
        
        if time_freq_positions is None:
            # 假设tokens来自规则的时-频网格
            time_frames = int(math.sqrt(N * self.max_time_frames / self.max_freq_bins))
            freq_bins = N // time_frames
            
            time_pos = torch.arange(time_frames, device=x.device).repeat_interleave(freq_bins)
            freq_pos = torch.arange(freq_bins, device=x.device).repeat(time_frames)
            
            time_freq_positions = torch.stack([time_pos, freq_pos], dim=1)  # [N, 2]
            time_freq_positions = time_freq_positions.unsqueeze(0).expand(B, -1, -1)
        
        # 获取时-频位置编码
        time_coords = time_freq_positions[:, :, 0].long()  # [B, N]
        freq_coords = time_freq_positions[:, :, 1].long()  # [B, N]
        
        time_embed = self.time_embed[time_coords]  # [B, N, half_dim]
        freq_embed = self.freq_embed[freq_coords]  # [B, N, half_dim]
        
        # 拼接时间和频率编码
        spectral_embed = torch.cat([time_embed, freq_embed], dim=-1)  # [B, N, D]
        
        return x + spectral_embed


class MultiModalPositionalEncoding(nn.Module):
    """
    多模态位置编码
    
    为不同模态提供不同的位置编码方案
    """
    
    def __init__(
        self,
        embed_dim: int,
        visual_config: dict = None,
        audio_config: dict = None
    ):
        super().__init__()
        
        self.embed_dim = embed_dim
        
        # 视觉位置编码（空间）
        visual_config = visual_config or {}
        self.visual_pos_enc = SpatialPositionalEncoding(
            embed_dim=embed_dim,
            **visual_config
        )
        
        # 音频位置编码（频谱）
        audio_config = audio_config or {}
        self.audio_pos_enc = SpectralPositionalEncoding(
            embed_dim=embed_dim,
            **audio_config
        )
        
        # 模态标识符
        self.modal_type_embed = nn.Parameter(torch.zeros(2, embed_dim))  # [visual, audio]
        nn.init.trunc_normal_(self.modal_type_embed, std=0.02)
    
    def forward(
        self,
        visual_tokens: torch.Tensor,
        audio_tokens: torch.Tensor,
        visual_positions: Optional[torch.Tensor] = None,
        audio_positions: Optional[torch.Tensor] = None
    ) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        为视觉和音频tokens添加各自的位置编码
        
        Args:
            visual_tokens: [B, N_v, D]
            audio_tokens: [B, N_a, D]
            visual_positions: [B, N_v, 2] 视觉空间位置
            audio_positions: [B, N_a, 2] 音频时-频位置
            
        Returns:
            encoded_visual: [B, N_v, D]
            encoded_audio: [B, N_a, D]
        """
        # 添加模态类型嵌入
        visual_tokens = visual_tokens + self.modal_type_embed[0]
        audio_tokens = audio_tokens + self.modal_type_embed[1]
        
        # 添加各自的位置编码
        encoded_visual = self.visual_pos_enc(visual_tokens, visual_positions)
        encoded_audio = self.audio_pos_enc(audio_tokens, audio_positions)
        
        return encoded_visual, encoded_audio


# 便利函数
def create_positional_encoding(
    encoding_type: str,
    embed_dim: int,
    **kwargs
) -> nn.Module:
    """
    创建位置编码的便利函数
    
    Args:
        encoding_type: 编码类型
        embed_dim: 嵌入维度
        **kwargs: 其他参数
        
    Returns:
        位置编码模块
    """
    if encoding_type == "sinusoidal":
        return PositionalEncoding(embed_dim, **kwargs)
    elif encoding_type == "learnable":
        return LearnablePositionalEncoding(embed_dim, **kwargs)
    elif encoding_type == "relative":
        return RelativePositionalEncoding(embed_dim, **kwargs)
    elif encoding_type == "spatial":
        return SpatialPositionalEncoding(embed_dim, **kwargs)
    elif encoding_type == "temporal":
        return TemporalPositionalEncoding(embed_dim, **kwargs)
    elif encoding_type == "spectral":
        return SpectralPositionalEncoding(embed_dim, **kwargs)
    elif encoding_type == "multimodal":
        return MultiModalPositionalEncoding(embed_dim, **kwargs)
    else:
        raise ValueError(f"Unknown encoding_type: {encoding_type}")