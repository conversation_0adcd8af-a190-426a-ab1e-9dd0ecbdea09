"""
RoPE (Rotary Position Embedding) 支持的Transformer
提供相对位置编码能力，增强模型的位置感知
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import math


def apply_rotary_pos_emb(x, cos, sin):
    """
    应用旋转位置编码
    Args:
        x: [B, N, H, D] 输入张量
        cos: [N, D] 余弦编码
        sin: [N, D] 正弦编码
    Returns:
        x_rotated: 应用RoPE后的张量
    """
    # 分离x的前一半和后一半
    x1, x2 = x.chunk(2, dim=-1)
    
    # 应用旋转变换
    # [cos * x1 - sin * x2, sin * x1 + cos * x2]
    cos = cos.unsqueeze(0).unsqueeze(2)  # [1, N, 1, D/2]
    sin = sin.unsqueeze(0).unsqueeze(2)  # [1, N, 1, D/2]
    
    x_rotated = torch.cat([
        cos * x1 - sin * x2,
        sin * x1 + cos * x2
    ], dim=-1)
    
    return x_rotated


class RoPEAttention(nn.Module):
    """
    支持RoPE的Multi-Head Attention
    """
    
    def __init__(
        self,
        embed_dim: int,
        num_heads: int,
        dropout: float = 0.1,
        rope_theta: float = 10000.0,
        max_seq_len: int = 2048
    ):
        super().__init__()
        
        self.embed_dim = embed_dim
        self.num_heads = num_heads
        self.head_dim = embed_dim // num_heads
        self.rope_theta = rope_theta
        self.max_seq_len = max_seq_len
        
        assert self.head_dim % 2 == 0, "head_dim必须是偶数以支持RoPE"
        
        # Linear layers
        self.q_proj = nn.Linear(embed_dim, embed_dim)
        self.k_proj = nn.Linear(embed_dim, embed_dim)
        self.v_proj = nn.Linear(embed_dim, embed_dim)
        self.out_proj = nn.Linear(embed_dim, embed_dim)
        
        self.dropout = nn.Dropout(dropout)
        self.scale = self.head_dim ** -0.5
        
        # 预计算RoPE编码
        self.register_buffer("cos_cache", None)
        self.register_buffer("sin_cache", None)
        self._build_rope_cache(max_seq_len)
    
    def _build_rope_cache(self, seq_len):
        """构建RoPE缓存"""
        pos = torch.arange(seq_len, dtype=torch.float32)
        dim = torch.arange(0, self.head_dim, 2, dtype=torch.float32)
        
        # 计算频率
        freqs = 1.0 / (self.rope_theta ** (dim / self.head_dim))
        
        # 外积得到位置编码矩阵
        pos_freqs = torch.outer(pos, freqs)
        
        # 计算cos和sin
        cos = torch.cos(pos_freqs)
        sin = torch.sin(pos_freqs)
        
        self.register_buffer("cos_cache", cos)
        self.register_buffer("sin_cache", sin)
    
    def forward(self, query, key, value, attn_mask=None, need_weights=False):
        """
        前向传播
        Args:
            query: [B, N, D]
            key: [B, N, D]
            value: [B, N, D]
            attn_mask: 注意力掩码
            need_weights: 是否返回注意力权重
        Returns:
            output: [B, N, D]
            attn_weights: 注意力权重 (如果need_weights=True)
        """
        B, N, D = query.shape
        
        # 线性投影
        q = self.q_proj(query).view(B, N, self.num_heads, self.head_dim).transpose(1, 2)  # [B, H, N, D_h]
        k = self.k_proj(key).view(B, N, self.num_heads, self.head_dim).transpose(1, 2)
        v = self.v_proj(value).view(B, N, self.num_heads, self.head_dim).transpose(1, 2)
        
        # 获取RoPE编码
        if N > self.cos_cache.size(0):
            self._build_rope_cache(N)
        
        cos = self.cos_cache[:N]  # [N, D_h/2]
        sin = self.sin_cache[:N]  # [N, D_h/2]
        
        # 应用RoPE到query和key
        q_rot = apply_rotary_pos_emb(q, cos, sin)
        k_rot = apply_rotary_pos_emb(k, cos, sin)
        
        # 计算注意力分数
        attn_scores = torch.matmul(q_rot, k_rot.transpose(-2, -1)) * self.scale  # [B, H, N, N]
        
        # 应用掩码
        if attn_mask is not None:
            attn_scores = attn_scores.masked_fill(attn_mask == 0, float('-inf'))
        
        # Softmax
        attn_weights = F.softmax(attn_scores, dim=-1)
        attn_weights = self.dropout(attn_weights)
        
        # 应用注意力权重到value
        output = torch.matmul(attn_weights, v)  # [B, H, N, D_h]
        
        # 重新组织输出
        output = output.transpose(1, 2).contiguous().view(B, N, D)  # [B, N, D]
        output = self.out_proj(output)
        
        if need_weights:
            # 平均所有头的注意力权重
            attn_weights = attn_weights.mean(dim=1)  # [B, N, N]
            return output, attn_weights
        else:
            return output


class RoPETransformerEncoderLayer(nn.Module):
    """
    支持RoPE的Transformer Encoder层
    """
    
    def __init__(
        self,
        embed_dim: int,
        num_heads: int,
        feedforward_dim: int = None,
        dropout: float = 0.1,
        activation: str = "gelu",
        rope_theta: float = 10000.0,
        max_seq_len: int = 2048
    ):
        super().__init__()
        
        if feedforward_dim is None:
            feedforward_dim = embed_dim * 4
        
        # RoPE Self-Attention
        self.self_attn = RoPEAttention(
            embed_dim=embed_dim,
            num_heads=num_heads,
            dropout=dropout,
            rope_theta=rope_theta,
            max_seq_len=max_seq_len
        )
        
        # Feed Forward Network
        self.linear1 = nn.Linear(embed_dim, feedforward_dim)
        self.dropout1 = nn.Dropout(dropout)
        self.linear2 = nn.Linear(feedforward_dim, embed_dim)
        self.dropout2 = nn.Dropout(dropout)
        
        # Layer Normalization
        self.norm1 = nn.LayerNorm(embed_dim)
        self.norm2 = nn.LayerNorm(embed_dim)
        
        # Activation
        if activation == "gelu":
            self.activation = F.gelu
        elif activation == "relu":
            self.activation = F.relu
        else:
            raise ValueError(f"不支持的激活函数: {activation}")
    
    def forward(self, src, src_mask=None, need_weights=False):
        """
        前向传播
        Args:
            src: [B, N, D]
            src_mask: 源序列掩码
            need_weights: 是否返回注意力权重
        Returns:
            output: [B, N, D]
            attn_weights: 注意力权重 (如果need_weights=True)
        """
        # Self-Attention
        if need_weights:
            attn_output, attn_weights = self.self_attn(
                src, src, src, attn_mask=src_mask, need_weights=True
            )
        else:
            attn_output = self.self_attn(src, src, src, attn_mask=src_mask)
        
        # Add & Norm
        src = self.norm1(src + attn_output)
        
        # Feed Forward
        ff_output = self.linear2(self.dropout1(self.activation(self.linear1(src))))
        ff_output = self.dropout2(ff_output)
        
        # Add & Norm
        output = self.norm2(src + ff_output)
        
        if need_weights:
            return output, attn_weights
        else:
            return output


class RoPETransformerEncoder(nn.Module):
    """
    支持RoPE的Transformer Encoder
    """
    
    def __init__(
        self,
        embed_dim: int,
        num_layers: int,
        num_heads: int,
        feedforward_dim: int = None,
        dropout: float = 0.1,
        activation: str = "gelu",
        rope_theta: float = 10000.0,
        max_seq_len: int = 2048
    ):
        super().__init__()
        
        self.num_layers = num_layers
        
        # 构建Transformer层
        self.layers = nn.ModuleList([
            RoPETransformerEncoderLayer(
                embed_dim=embed_dim,
                num_heads=num_heads,
                feedforward_dim=feedforward_dim,
                dropout=dropout,
                activation=activation,
                rope_theta=rope_theta,
                max_seq_len=max_seq_len
            )
            for _ in range(num_layers)
        ])
    
    def forward(self, src, mask=None, need_weights=False):
        """
        前向传播
        Args:
            src: [B, N, D]
            mask: 输入掩码
            need_weights: 是否返回所有层的注意力权重
        Returns:
            output: [B, N, D]
            all_attn_weights: 所有层的注意力权重 (如果need_weights=True)
        """
        output = src
        all_attn_weights = []
        
        for layer in self.layers:
            if need_weights:
                output, attn_weights = layer(output, src_mask=mask, need_weights=True)
                all_attn_weights.append(attn_weights)
            else:
                output = layer(output, src_mask=mask)
        
        if need_weights:
            return output, all_attn_weights
        else:
            return output


def create_rope_transformer_encoder(
    embed_dim=768,
    num_layers=6,
    num_heads=8,
    rope_theta=10000.0,
    **kwargs
):
    """创建RoPE Transformer编码器的便捷函数"""
    return RoPETransformerEncoder(
        embed_dim=embed_dim,
        num_layers=num_layers,
        num_heads=num_heads,
        rope_theta=rope_theta,
        **kwargs
    )