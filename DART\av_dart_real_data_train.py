#!/usr/bin/env python3
"""
AV-DART 真实数据训练脚本

使用项目现有的音视频显著性数据集进行训练
数据集：DIEM、Coutrot_db1、Coutrot_db2、SumMe、ETMD_av、AVAD
"""

import sys
import os
import json

# 添加路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(current_dir)
sys.path.insert(0, project_root)
sys.path.insert(0, current_dir)

import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader, Dataset
import numpy as np
from tqdm import tqdm
import argparse
import time
import math
from collections import defaultdict
from datetime import datetime
import warnings


from av_dart.tokenizers.enhanced_av_tokenizer import EnhancedAVTokenizer
from av_dart.models.av_dart_model import AVDARTModel 
from av_dart.models.saliency_decoder import SaliencyDecoder
NEW_COMPONENTS_AVAILABLE = True

from av_dart.tokenizers.av_tokenizer import create_simple_av_tokenizer
from av_dart.models.budget import create_simple_budget_controller

REAL_DART_AVAILABLE = True


# 使用绝对导入避免与内置包冲突
import sys
import os
sys.path.insert(0, project_root)  # 确保项目根目录在路径中

from datasets.saliency_db import saliency_db_spec
from datasets.prepare_data import get_av_dataset, get_training_av_loader
DATASETS_AVAILABLE = True

import torch.nn.functional as F

# 简化导入，只保留基本功能
sys.path.insert(0, os.path.join(project_root, 'util'))
# try:
from util.utils import AverageMeter, AverageMeterList
UTILS_AVAILABLE = True


def complete_saliency_loss(pred_saliency, target_saliency, loss_weights=[1.0, 1.0, 1.0, 1.0]):
    """完整的显著性损失函数，包含多个评价指标"""
    kl_weight, cc_weight, sim_weight, nss_weight = loss_weights
    
    # 导入原始损失函数
    from models.sal_losses import kldiv2, cc_s2, similarity2, nss2
    
    # 1. 处理额外的batch维度
    if len(target_saliency.shape) == 5:  # [1, B, C, H, W] -> [B, C, H, W]
        target_saliency = target_saliency.squeeze(0)
    
    # 2. 处理时间维度
    if len(target_saliency.shape) == 5:  # [B, T, C, H, W] -> [B, C, H, W]
        target_saliency = target_saliency.mean(dim=1)
    
    # 3. 修复batch维度不匹配问题
    if pred_saliency.shape[0] != target_saliency.shape[0]:
        if target_saliency.shape[0] == 1 and pred_saliency.shape[0] > 1:
            target_saliency = target_saliency.repeat(pred_saliency.shape[0], 1, 1, 1)
        elif pred_saliency.shape[0] == 1 and target_saliency.shape[0] > 1:
            pred_saliency = pred_saliency.repeat(target_saliency.shape[0], 1, 1, 1)
    
    # 4. 处理空间维度不匹配
    if pred_saliency.shape[-2:] != target_saliency.shape[-2:]:
        target_saliency = torch.nn.functional.interpolate(
            target_saliency, 
            size=pred_saliency.shape[-2:],
            mode='bilinear', 
            align_corners=False
        )
    
    # 5. 确保数据格式正确
    if len(pred_saliency.shape) == 4 and len(target_saliency.shape) == 4:
        # [B, C, H, W] -> [B, 1, H, W] 
        if pred_saliency.shape[1] != 1:
            pred_saliency = pred_saliency.mean(dim=1, keepdim=True)
        if target_saliency.shape[1] != 1:
            target_saliency = target_saliency.mean(dim=1, keepdim=True)
    
    # 计算各项损失
    try:
        kl_loss = kldiv2(pred_saliency, target_saliency) * kl_weight
        cc_loss = cc_s2(pred_saliency, target_saliency) * cc_weight  
        sim_loss = similarity2(pred_saliency, target_saliency) * sim_weight
        nss_loss = nss2(pred_saliency, target_saliency) * nss_weight
        
        # 总损失
        total_loss = kl_loss + cc_loss + sim_loss + nss_loss
        
        return {
            'total': total_loss,
            'main': kl_loss,
            'kl': kl_loss,
            'cc': cc_loss, 
            'sim': sim_loss,
            'nss': nss_loss
        }
    except Exception as e:
        print(f"完整损失计算失败，回退到MSE: {e}")
        # 回退到简单损失
        mse_loss = torch.nn.functional.mse_loss(pred_saliency, target_saliency)
        return {'total': mse_loss, 'main': mse_loss}



# 使用真正的ScorePredNet
def create_mobilenet_spn():
    """创建MobileNetSmall作为SPN"""
    import torchvision.models as models
    
    # 使用MobileNet V3 Small作为特征提取器
    mobilenet = models.mobilenet_v3_small(pretrained=True)
    features = mobilenet.features[:11]  # 使用前11层
    
    # 冻结特征提取器参数
    for param in features.parameters():
        param.requires_grad = False
        
    # 创建MLP用于分数预测
    class MLP(nn.Module):
        def __init__(self, input_dim, hidden_dim, output_dim):
            super().__init__()
            self.fc1 = nn.Linear(input_dim, hidden_dim)
            self.fc2 = nn.Linear(hidden_dim, output_dim)
            
        def forward(self, x):
            x = F.relu(self.fc1(x))
            return self.fc2(x)
    
    # 创建ScorePredNet类
    class MobileNetSmallSPN(nn.Module):
        def __init__(self):
            super().__init__()
            self.features = features
            self.mlp = MLP(96, 96, 1)  # MobileNetV3-Small第11层输出96个通道
            
        def forward(self, x, shape=(14, 14)):
            with torch.no_grad():
                x = self.features(x)
            
            # 将特征图转换为分数
            x = x.permute(0, 2, 3, 1)  # [B, H, W, C]
            x = self.mlp(x)  # [B, H, W, 1]
            x = x.permute(0, 3, 1, 2)  # [B, 1, H, W]
            
            # 插值到目标形状
            x = F.interpolate(x, size=shape, mode='bilinear', align_corners=False)
            
            # 标准化
            x = x - x.mean()
            x = x / (x.std() + 1e-8)
            x = F.sigmoid(x) + 0.1  # 确保正值
            
            return x.view(x.size(0), -1)  # [B, 196]
    
    return MobileNetSmallSPN()


def create_data_config():
    """创建数据配置"""
    # 加载数据集配置
    config_path = os.path.join(project_root, 'cfgs', 'dataset.json')
    
    with open(config_path, 'r') as f:
        data_config = json.load(f)
    print(f"✓ 加载数据配置: {config_path}")
    return data_config


def fix_audio_shape(audio_data):
    """
    修复音频数据维度，确保输出符合tokenizer要求的格式
    tokenizer期望: [B, T, F] 或 [B, 1, T, F]
    """
    
    # 已是 [B, T, F]
    if audio_data.dim() == 3:
        return audio_data

    # [B, 1, T, F]
    if audio_data.dim() == 4:
        B, C, T, F = audio_data.shape
        if C == 1:
            # 统一返回 [B, 1, T, F]
            return audio_data
        else:
            # 假定为 [B, T, H, W] -> [B, 1, T, H*W]
            B, T, H, W = audio_data.shape
            audio_data = audio_data.view(B, 1, T, H * W)
            return audio_data

    # [B, 1, T, H, W]（来自 mel 特征堆叠后的批处理）
    if audio_data.dim() == 5:
        B, C, T, H, W = audio_data.shape
        if C == 1:
            audio_data = audio_data.view(B, 1, T, H * W)
            return audio_data
        else:
            raise ValueError(f"无法处理的5维音频形状(需通道为1): {audio_data.shape}")

    # [B, F] -> [B, 1, 1, F]
    if audio_data.dim() == 2:
        audio_data = audio_data.unsqueeze(1).unsqueeze(1)
        return audio_data

    # 其他情况直接报错，避免猜测性重排导致不整除 view
    raise ValueError(f"无法处理的音频形状: {audio_data.shape}")



def train_step(model, batch, optimizer, device, use_full_loss=False, loss_weights=[1.0, 1.0, 1.0, 1.0], scaler=None):
    """增强的训练步骤，支持完整损失函数"""
    model.train()
    
    # 解析batch
    data_dict, target_dict = batch
    visual_data = data_dict['rgb'].to(device)
    audio_data = data_dict['audio'].to(device)
    target_saliency = target_dict['salmap'].to(device)
    
    # 修复音频维度
    audio_data = fix_audio_shape(audio_data)
    

    loss_func = lambda pred, target: complete_saliency_loss(pred, target, loss_weights)
    
    if scaler:  # 混合精度训练
        with torch.cuda.amp.autocast():
            pred_saliency = model(visual_data, audio_data)
            loss_dict = loss_func(pred_saliency, target_saliency)
        
        optimizer.zero_grad()
        scaler.scale(loss_dict['total']).backward()
        scaler.step(optimizer)
        scaler.update()
    else:
        pred_saliency = model(visual_data, audio_data)
        loss_dict = loss_func(pred_saliency, target_saliency)
        
        optimizer.zero_grad()
        loss_dict['total'].backward()
        optimizer.step()
    
    return {k: v.item() if torch.is_tensor(v) else v for k, v in loss_dict.items()}


def create_dataloader(data_config, batch_size=4, mode='train', datasets=['diem']):
    """创建数据加载器，支持多数据集联合训练"""
    
    all_datasets = []
    
    # 数据集名称映射
    dataset_name_mapping = {
        'diem': 'diem',
        'coutrot_db1': 'coutrot1', 
        'coutrot_db2': 'coutrot2',
        'summe': 'summe',
        'etmd_av': 'etmd',
        'avad': 'avad'
    }
    
    for dataset_name in datasets:
        # 映射数据集名称
        mapped_name = dataset_name_mapping.get(dataset_name, dataset_name)
        
        if mode == 'train':
            # 使用项目现有的训练数据加载器
            data_config_copy = data_config.copy()
            data_config_copy['dataset'] = mapped_name
            
            dataset = get_av_dataset(data_config_copy, is_training=True)
            all_datasets.append(dataset)
            
        else:
            # 验证集
            data_config_copy = data_config.copy()
            data_config_copy['dataset'] = mapped_name
            
            dataset = get_av_dataset(data_config_copy, is_training=False)
            all_datasets.append(dataset)
    
    # 合并所有数据集
    if len(all_datasets) > 1:
        from torch.utils.data import ConcatDataset
        combined_dataset = ConcatDataset(all_datasets)
    else:
        combined_dataset = all_datasets[0]
        
    dataloader = DataLoader(
        combined_dataset,
        batch_size=batch_size,
        shuffle=(mode == 'train'),
        num_workers=4,
        drop_last=True,
        pin_memory=True,
        persistent_workers=True
    )
    return dataloader


def evaluate_model(model, val_loader, device):
    """模型性能评估，与diffusion_trainer保持一致"""
    model.eval()
    losses = []
    
    with torch.no_grad():
        for batch in tqdm(val_loader, desc="Validation", leave=False):
            data_dict, target_dict = batch
            visual_data = data_dict['rgb'].to(device)
            audio_data = data_dict['audio'].to(device) 
            target_saliency = target_dict['salmap'].to(device)
            
            # 修复音频维度
            audio_data = fix_audio_shape(audio_data)
            
            pred_saliency = model(visual_data, audio_data)
            loss_dict = complete_saliency_loss(pred_saliency, target_saliency)
            losses.append(loss_dict['total'].item())
    
    return np.mean(losses) if losses else 0.0



def main():
    parser = argparse.ArgumentParser(description='AV-DART 真实数据训练')
    parser.add_argument('--batch_size', type=int, default=64, help='批次大小')
    parser.add_argument('--epochs', type=int, default=100, help='训练轮数')
    parser.add_argument('--lr', type=float, default=2e-4, help='学习率')
    parser.add_argument('--weight_decay', type=float, default=0.01, help='权重衰减')
    parser.add_argument('--warmup_epochs', type=int, default=10, help='学习率预热轮数')
    parser.add_argument('--device', type=str, default='4,5,6,7', help='GPU设备ID，多个用逗号分隔，如 0,1,2,3')
    parser.add_argument('--embed_dim', type=int, default=768, help='嵌入维度')
    parser.add_argument('--total_tokens', type=int, default=196, help='总token数')
    parser.add_argument('--test_only', action='store_true', help='仅测试模式')
    parser.add_argument('--resume', type=str, default='', help='恢复训练的检查点路径')
    parser.add_argument('--save_dir', type=str, default='./checkpoints', help='模型保存目录')
    parser.add_argument('--log_interval', type=int, default=10, help='日志记录间隔')
    parser.add_argument('--eval_interval', type=int, default=5, help='验证间隔(epochs)')
    parser.add_argument('--datasets', nargs='+', default=['diem', 'coutrot_db1', 'coutrot_db2', 'summe', 'etmd_av', 'avad'], 
                       choices=['diem', 'coutrot_db1', 'coutrot_db2', 'summe', 'etmd_av', 'avad'],
                       help='使用的数据集列表')
    parser.add_argument('--use_amp', action='store_true', help='使用自动混合精度训练')
    parser.add_argument('--eval_individual', action='store_true', help='单独验证每个数据集')
    # === Token分配策略参数 ===
    parser.add_argument('--token_strategy', type=str, default='energy_based', 
                       choices=['fixed', 'dynamic', 'energy_based', 'adaptive', 'attention_based'],
                       help='Token分配策略: fixed(固定), dynamic(动态), energy_based(能量驱动)')
    parser.add_argument('--visual_ratio', type=float, default=0.6, 
                       help='固定分配时的视觉token比例 (0.1-0.9)')
    parser.add_argument('--energy_weight', type=float, default=0.4,
                       help='能量驱动策略的调整权重 (0.1-0.8)')
    parser.add_argument('--min_visual_ratio', type=float, default=0.3,
                       help='动态分配时最小视觉token比例')
    parser.add_argument('--max_visual_ratio', type=float, default=0.8,
                       help='动态分配时最大视觉token比例')
    parser.add_argument('--quiet', action='store_true', help='减少输出信息')
    parser.add_argument('--plot_curves', action='store_true', help='绘制并保存训练损失曲线')
    parser.add_argument('--view_curves', type=str, default='', help='查看指定目录下的训练曲线')

    
    args = parser.parse_args()
    

    
    # 创建数据配置
    data_config = create_data_config()
    
    # 准备token分配策略参数
    strategy_kwargs = {
        'visual_ratio': args.visual_ratio,
        'energy_weight': args.energy_weight, 
        'min_visual_ratio': args.min_visual_ratio,
        'max_visual_ratio': args.max_visual_ratio
    }
    
    # 创建模型 - 支持选择新的模块化组件
    print(f"\n=== 创建AV-DART模型 ===")
    print(f"使用Token分配策略: {args.token_strategy}")
    if args.token_strategy == 'fixed':
        print(f"  视觉Token比例: {args.visual_ratio:.1%}")
    elif args.token_strategy == 'energy_based':
        print(f"  能量调整权重: {args.energy_weight}")
    elif args.token_strategy == 'dynamic':
        print(f"  视觉比例范围: {args.min_visual_ratio:.1%} - {args.max_visual_ratio:.1%}")
    

    model = AVDARTModel(
        embed_dim=args.embed_dim,
        total_tokens=args.total_tokens,
        num_layers=6,
        num_heads=8,
        output_size=(224, 384),  # 指定输出尺寸为224x384
        token_strategy=args.token_strategy,  # 新增参数
        **strategy_kwargs  # 传递策略特定参数
    )
    
    # 设备选择和多卡设置
    if torch.cuda.is_available():
        # 解析GPU设备
        if ',' in args.device:
            # 多卡训练
            gpu_ids = [int(x.strip()) for x in args.device.split(',')]
            device = torch.device(f'cuda:{gpu_ids[0]}')  # 使用第一张卡作为主设备
            print(f"使用多卡训练: {gpu_ids}")
            # 如果有多个GPU，使用DataParallel
            if len(gpu_ids) > 1:
                try:
                    model = torch.nn.DataParallel(model, device_ids=gpu_ids)
                    print("成功启用DataParallel多卡训练")
                except Exception as e:
                    print(f"启用DataParallel失败，回退到单卡训练: {e}")
                    device = torch.device(f'cuda:{gpu_ids[0]}')
        else:
            # 单卡训练
            gpu_id = int(args.device)
            device = torch.device(f'cuda:{gpu_id}')
            print(f"使用单卡训练: cuda:{gpu_id}")
    else:
        device = torch.device('cpu')
        print("CUDA不可用，使用CPU训练")
    
    # 将模型移动到设备
    model = model.to(device)
    
    # 创建数据加载器
    train_loader = create_dataloader(data_config, args.batch_size, 'train', args.datasets)
    val_loader = create_dataloader(data_config, args.batch_size, 'val', args.datasets)
    
    # 创建优化器和学习率调度器
    optimizer = optim.AdamW(model.parameters(), lr=args.lr, weight_decay=args.weight_decay)
    
    # 学习率调度器：预热 + 余弦退火
    total_steps = len(train_loader) * args.epochs
    warmup_steps = min(len(train_loader) * args.warmup_epochs, total_steps // 2)  # 限制预热步数
    
    
    def lr_lambda(step):
        # 简化调试输出，移除详细信息
        if step < warmup_steps:
            # 预热阶段：线性增加到初始学习率
            lr = step / max(1, warmup_steps) if warmup_steps > 0 else 1.0
            return lr
        else:
            # 余弦退火阶段
            denominator = max(1, total_steps - warmup_steps)
            if denominator == 0:
                return 1.0  # 如果没有余弦退火阶段，保持学习率不变
            progress = (step - warmup_steps) / denominator
            # 确保进度在[0, 1]范围内
            progress = max(0.0, min(1.0, progress))
            lr = 0.5 * (1 + math.cos(math.pi * progress))
            return lr
    
    scheduler = optim.lr_scheduler.LambdaLR(optimizer, lr_lambda)
    
    # 混合精度训练
    scaler = None
    if args.use_amp:
        try:
            from torch.cuda.amp import GradScaler
            scaler = GradScaler()
        except ImportError:
            print("警告: 无法导入GradScaler，禁用混合精度训练")
            args.use_amp = False
    
    # 创建检查点目录
    import os
    os.makedirs(args.save_dir, exist_ok=True)
    
    # 恢复训练
    start_epoch = 0
    best_val_loss = float('inf')
    if args.resume:
        if os.path.exists(args.resume):
            checkpoint = torch.load(args.resume, map_location=device)
            model.load_state_dict(checkpoint['model_state_dict'])
            optimizer.load_state_dict(checkpoint['optimizer_state_dict'])
            scheduler.load_state_dict(checkpoint['scheduler_state_dict'])
            start_epoch = checkpoint['epoch'] + 1
            best_val_loss = checkpoint.get('best_val_loss', float('inf'))
        else:
            print(f"  ✗ 检查点文件不存在: {args.resume}")
    
    print(f"\n=== 开始训练 ===")
    
    # 训练历史记录
    train_history = {
        'train_loss': [],
        'val_loss': [],
        'learning_rate': []
    }
    
    # 训练循环
    for epoch in range(start_epoch, args.epochs):

        # 训练阶段
        model.train()
        train_losses = []
        train_progress = tqdm(train_loader, desc=f"Training Epoch {epoch+1}", 
                             leave=True, dynamic_ncols=True,
                             bar_format='{l_bar}{bar:30}{r_bar}{bar:-30b}')
        
        for batch_idx, batch in enumerate(train_progress):
            # 使用混合精度训练
            if args.use_amp and scaler:
                with torch.cuda.amp.autocast():
                    loss_dict = train_step(model, batch, optimizer, device, scaler=scaler)
            else:
                loss_dict = train_step(model, batch, optimizer, device)
            
            train_losses.append(loss_dict.get('total', 0))
            
            # 更新学习率（在optimizer.step()之后）
            scheduler.step()
            
            train_progress.set_postfix({
                'loss': f"{loss_dict.get('total', 0):.4f}",
                'lr': f"{optimizer.param_groups[0]['lr']:.2e}"
            })
            
            # 记录训练日志 - 只在quiet模式下完全禁用
            if batch_idx % args.log_interval == 0 and batch_idx > 0:
                pass  # 已经在进度条中显示，不再额外打印
        # 记录训练统计
        if train_losses:
            # 将可能的tensor转换为CPU上的数值
            train_losses_cpu = [loss.cpu().item() if torch.is_tensor(loss) else loss for loss in train_losses]
            avg_train_loss = np.mean(train_losses_cpu)
        else:
            avg_train_loss = 0.0
        train_history['train_loss'].append(avg_train_loss)
        current_lr = optimizer.param_groups[0]['lr']  # 获取当前学习率
        train_history['learning_rate'].append(current_lr)
        
        # 验证阶段（每隔几个epoch进行一次）
        avg_val_loss = 0.0
        if (epoch + 1) % args.eval_interval == 0:
            model.eval()
            val_losses = []
            val_progress = tqdm(val_loader, desc=f"Validation Epoch {epoch+1}", 
                               leave=True, dynamic_ncols=True,
                               bar_format='{l_bar}{bar:30}{r_bar}{bar:-30b}')
            
            with torch.no_grad():
                for batch_idx, batch in enumerate(val_progress):
                    # 容错处理不同的batch格式
                    try:
                        # 处理字典列表格式: [{sample1}, {sample2}, ...]
                        if isinstance(batch, list) and len(batch) > 0 and isinstance(batch[0], dict):
                            
                            # 提取各个字段的数据
                            visual_list = []
                            audio_list = []
                            target_list = []
                            
                            for sample in batch:
                                # 提取视觉数据
                                if 'rgb' in sample:
                                    visual_list.append(sample['rgb'])
                                elif 'video' in sample:
                                    visual_list.append(sample['video'])
                                else:
                                    continue  # 跳过没有视觉数据的样本
                                
                                # 提取音频数据
                                if 'audio' in sample:
                                    audio_list.append(sample['audio'])
                                else:
                                    # 生成虚拟音频数据
                                    audio_list.append(torch.randn(9, 21504))
                                
                                # 提取显著性标签
                                if 'salmap' in sample:
                                    target_list.append(sample['salmap'])
                                elif 'saliency' in sample:
                                    target_list.append(sample['saliency'])
                                else:
                                    # 生成虚拟标签
                                    if visual_list:
                                        if visual_list[-1].dim() >= 2:
                                            H, W = visual_list[-1].shape[-2:]
                                        else:
                                            H, W = 224, 224
                                        target_list.append(torch.rand(1, H, W))
                            
                            # 合并数据
                            if visual_list:
                                visual_data = torch.stack(visual_list, dim=0).to(device)
                                audio_data = torch.stack(audio_list, dim=0).to(device)
                                target_saliency = torch.stack(target_list, dim=0).to(device)
                            else:
                                continue  # 没有有效数据，跳过这个batch
                        
                        # 处理标准DataLoader格式: (data_dict, target_dict)
                        elif isinstance(batch, (list, tuple)) and len(batch) == 2:
                            data_dict, target_dict = batch
                            visual_data = data_dict.get('rgb', data_dict.get('video')).to(device)
                            audio_data = data_dict.get('audio').to(device)
                            target_saliency = target_dict.get('salmap', target_dict.get('saliency')).to(device)
                            # 获取fixation数据（如果存在）
                            fixation_data = target_dict.get('fixation', None)
                            if fixation_data is not None:
                                fixation_data = fixation_data.to(device)
                        
                        # 处理单一字典格式
                        elif isinstance(batch, dict):
                            visual_data = batch.get('rgb', batch.get('video'))
                            audio_data = batch.get('audio')
                            target_saliency = batch.get('saliency', batch.get('salmap'))
                            
                            if visual_data is None:
                                continue
                                
                            visual_data = visual_data.to(device)
                            if audio_data is not None:
                                audio_data = audio_data.to(device)
                            else:
                                B = visual_data.size(0)
                                audio_data = torch.randn(B, 9, 21504, device=device)
                            
                            if target_saliency is not None:
                                target_saliency = target_saliency.to(device)
                            else:
                                B = visual_data.size(0)
                                target_saliency = torch.rand(B, 1, 224, 224, device=device)
                        
                        else:
                            continue
                        
                        # 统一修复音频维度
                        audio_data = fix_audio_shape(audio_data)
                        # 前向传播和损失计算
                        pred_saliency = model(visual_data, audio_data)
                        
                        # 计算简化损失（占位代替更复杂指标）
                        loss_dict = complete_saliency_loss(pred_saliency, target_saliency)
                        val_losses.append(loss_dict.get('total', 0))
                        
                        val_progress.set_postfix({
                            'val_loss': f"{loss_dict.get('total', 0):.3f}",
                            'kl': f"{loss_dict.get('kl', 0):.3f}", 
                            'cc': f"{loss_dict.get('cc', 0):.3f}",
                            'sim': f"{loss_dict.get('sim', 0):.3f}",
                            'nss': f"{loss_dict.get('nss', 0):.3f}",
                            'auc_j': f"{loss_dict.get('auc_j', 0):.3f}"
                        })
                        
                    except Exception as e:
                        if True:
                            pass  # 验证时静默处理错误
                        continue
            # 记录验证统计
            if val_losses:
                # 将CUDA tensor转换为CPU上的numpy数组
                val_losses_cpu = [loss.cpu().item() if torch.is_tensor(loss) else loss for loss in val_losses]
                avg_val_loss = np.mean(val_losses_cpu)
            else:
                avg_val_loss = 0.0
            train_history['val_loss'].append(avg_val_loss)
            
            # 计算验证指标的平均值
            val_metrics = {}
            if val_losses:  # 如果有验证数据
                # 收集最后一个batch的指标作为代表
                if 'loss_dict' in locals():
                    val_metrics = {
                        'kl': loss_dict.get('kl', 0),
                        'cc': loss_dict.get('cc', 0), 
                        'sim': loss_dict.get('sim', 0),
                        'nss': loss_dict.get('nss', 0),
                        'auc_j': loss_dict.get('auc_j', 0)
                    }
                    # 转换tensor为数值
                    for key, value in val_metrics.items():
                        if torch.is_tensor(value):
                            val_metrics[key] = value.cpu().item()
            
            if True:
                print(f"  验证损失: {avg_val_loss:.4f}")
                if val_metrics:
                    print(f"  验证指标 - KL: {val_metrics.get('kl', 0):.4f}, CC: {val_metrics.get('cc', 0):.4f}, SIM: {val_metrics.get('sim', 0):.4f}, NSS: {val_metrics.get('nss', 0):.4f}, AUC_J: {val_metrics.get('auc_j', 0):.4f}")
        
        # Epoch统计
        if True:
            print(f"  平均训练损失: {avg_train_loss:.4f}")
        if avg_val_loss > 0:
            print(f"  平均验证损失: {avg_val_loss:.4f}")
        
        # 保存检查点
        is_best = avg_val_loss < best_val_loss if avg_val_loss > 0 else False
        if is_best:
            best_val_loss = avg_val_loss
            
        # 定期保存检查点
        if (epoch + 1) % 10 == 0 or is_best:
            checkpoint = {
                'epoch': epoch,
                'model_state_dict': model.state_dict(),
                'optimizer_state_dict': optimizer.state_dict(),
                'scheduler_state_dict': scheduler.state_dict(),
                'train_loss': avg_train_loss,
                'val_loss': avg_val_loss,
                'best_val_loss': best_val_loss,
                'train_history': train_history,
                'args': vars(args)
            }
            
            # 保存检查点
            checkpoint_path = os.path.join(args.save_dir, f'checkpoint_epoch_{epoch+1}.pth')
            torch.save(checkpoint, checkpoint_path)
            
            # 保存最优模型
            if is_best:
                best_model_path = os.path.join(args.save_dir, 'best_model.pth')
                torch.save(checkpoint, best_model_path)
        
        # 显存统计
        if torch.cuda.is_available() and (epoch + 1) % 10 == 0 and not args.quiet:
            memory_allocated = torch.cuda.memory_allocated(device) / 1024**3
            memory_reserved = torch.cuda.memory_reserved(device) / 1024**3
            print(f"  显存使用: {memory_allocated:.2f}GB / {memory_reserved:.2f}GB")
            
    # 保存最终模型
    final_model_path = os.path.join(args.save_dir, 'final_model.pth')
    final_checkpoint = {
        'epoch': args.epochs - 1,
        'model_state_dict': model.state_dict(),
        'optimizer_state_dict': optimizer.state_dict(),
        'scheduler_state_dict': scheduler.state_dict(),
        'train_history': train_history,
        'args': vars(args)
    }
    torch.save(final_checkpoint, final_model_path)
    print(f"\n✓ 最终模型已保存: {final_model_path}")
    
    # 保存训练历史
    history_path = os.path.join(args.save_dir, 'training_history.json')
    with open(history_path, 'w') as f:
        json.dump(train_history, f, indent=2)
    print(f"✓ 训练历史已保存: {history_path}")
    


if __name__ == "__main__":
    # 抑制已知第三方库非关键告警
    warnings.filterwarnings(
        "ignore",
        message=r".*antialias parameter of all the resizing transforms.*"
    )
    warnings.filterwarnings(
        "ignore",
        message=r".*MMCV will release v2.0.0.*"
    )
    warnings.filterwarnings(
        "ignore",
        message=r".*TypedStorage is deprecated.*"
    )
    main()